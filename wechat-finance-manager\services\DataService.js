// services/DataService.js
import CryptoJS from 'crypto-js'

class DataService {
  constructor() {
    this.db = wx.cloud.database()
    this.encryptionKey = 'finance-manager-key-2024' // 在实际应用中应该使用更安全的密钥管理
    this.logger = this.initLogger()
  }

  // 初始化日志记录器
  initLogger() {
    return {
      info: (message, data = {}) => {
        console.log(`[DataService INFO] ${message}`, data)
        this.saveLog('info', message, data)
      },
      error: (message, error = {}) => {
        console.error(`[DataService ERROR] ${message}`, error)
        this.saveLog('error', message, error)
      },
      warn: (message, data = {}) => {
        console.warn(`[DataService WARN] ${message}`, data)
        this.saveLog('warn', message, data)
      }
    }
  }

  // 保存日志到云数据库
  async saveLog(level, message, data) {
    try {
      await this.db.collection('logs').add({
        data: {
          level,
          message,
          data: JSON.stringify(data),
          userId: this.getCurrentUserId(),
          timestamp: new Date(),
          source: 'DataService'
        }
      })
    } catch (error) {
      console.error('保存日志失败:', error)
    }
  }

  // 错误处理包装器
  async executeWithErrorHandling(operation, operationName) {
    try {
      this.logger.info(`开始执行操作: ${operationName}`)
      const result = await operation()
      this.logger.info(`操作成功完成: ${operationName}`, { result })
      return { success: true, data: result }
    } catch (error) {
      this.logger.error(`操作失败: ${operationName}`, error)
      return { 
        success: false, 
        error: {
          message: error.message || '操作失败',
          code: error.code || 'UNKNOWN_ERROR',
          details: error
        }
      }
    }
  }

  // 账户管理
  async createAccount(accountData) {
    return await this.executeWithErrorHandling(async () => {
      // 验证输入数据
      this.validateAccountData(accountData)
      
      // 加密敏感数据
      const encryptedData = {
        ...accountData,
        accountNumber: this.encryptData(accountData.accountNumber),
        userId: this.getCurrentUserId(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const result = await this.db.collection('accounts').add({
        data: encryptedData
      })
      
      return result
    }, 'createAccount')
  }

  async getAccounts(userId) {
    return await this.executeWithErrorHandling(async () => {
      const result = await this.db.collection('accounts')
        .where({
          userId: userId || this.getCurrentUserId()
        })
        .orderBy('createdAt', 'desc')
        .get()
      
      // 解密敏感数据
      const decryptedData = result.data.map(account => ({
        ...account,
        accountNumber: this.decryptData(account.accountNumber)
      }))
      
      return decryptedData
    }, 'getAccounts')
  }

  async updateAccount(accountId, data) {
    return await this.executeWithErrorHandling(async () => {
      // 验证账户ID
      if (!accountId) {
        throw new Error('账户ID不能为空')
      }

      // 加密敏感数据
      const updateData = { ...data }
      if (updateData.accountNumber) {
        updateData.accountNumber = this.encryptData(updateData.accountNumber)
      }

      const result = await this.db.collection('accounts')
        .doc(accountId)
        .update({
          data: {
            ...updateData,
            updatedAt: new Date()
          }
        })
      
      return result
    }, 'updateAccount')
  }

  async deleteAccount(accountId) {
    return await this.executeWithErrorHandling(async () => {
      // 验证账户ID
      if (!accountId) {
        throw new Error('账户ID不能为空')
      }

      // 检查是否有关联的持仓数据
      const positions = await this.db.collection('positions')
        .where({ accountId })
        .count()
      
      if (positions.total > 0) {
        throw new Error('无法删除包含持仓数据的账户，请先删除相关持仓')
      }

      const result = await this.db.collection('accounts')
        .doc(accountId)
        .remove()
      
      return result
    }, 'deleteAccount')
  }

  // 持仓管理
  async addPosition(positionData) {
    return await this.executeWithErrorHandling(async () => {
      // 创建Position实例进行验证
      const Position = require('../models/Position.js')
      const position = new Position({
        ...positionData,
        userId: this.getCurrentUserId()
      })

      // 验证持仓数据
      const validation = position.validate()
      if (!validation.isValid) {
        throw new Error(`持仓数据验证失败: ${validation.errors.join(', ')}`)
      }

      // 检查是否已存在相同证券的持仓
      const existingPositions = await this.db.collection('positions')
        .where({
          accountId: position.accountId,
          symbol: position.symbol,
          userId: this.getCurrentUserId()
        })
        .get()

      if (existingPositions.data.length > 0) {
        throw new Error('该账户已存在此证券的持仓')
      }

      // 重新计算相关数值
      position.recalculate()

      const result = await this.db.collection('positions').add({
        data: position.toDatabase()
      })
      
      return result
    }, 'addPosition')
  }

  async getPositions(accountId) {
    return await this.executeWithErrorHandling(async () => {
      if (!accountId) {
        throw new Error('账户ID不能为空')
      }

      const result = await this.db.collection('positions')
        .where({
          accountId: accountId,
          userId: this.getCurrentUserId()
        })
        .orderBy('createdAt', 'desc')
        .get()
      
      return result.data
    }, 'getPositions')
  }

  async updatePosition(positionId, updateData) {
    return await this.executeWithErrorHandling(async () => {
      if (!positionId) {
        throw new Error('持仓ID不能为空')
      }

      // 获取现有持仓数据
      const existingResult = await this.db.collection('positions')
        .doc(positionId)
        .get()

      if (!existingResult.data) {
        throw new Error('持仓不存在')
      }

      // 检查权限
      if (existingResult.data.userId !== this.getCurrentUserId()) {
        throw new Error('无权限操作此持仓')
      }

      // 创建Position实例
      const Position = require('../models/Position.js')
      const position = Position.fromDatabase(existingResult.data)

      // 如果更新证券代码，检查是否重复
      if (updateData.symbol && updateData.symbol !== position.symbol) {
        const duplicatePositions = await this.db.collection('positions')
          .where({
            accountId: position.accountId,
            symbol: updateData.symbol,
            userId: this.getCurrentUserId()
          })
          .get()

        if (duplicatePositions.data.length > 0) {
          throw new Error('该账户已存在此证券的持仓')
        }
      }

      // 更新持仓数据
      position.update(updateData)

      // 验证更新后的数据
      const validation = position.validate()
      if (!validation.isValid) {
        throw new Error(`持仓数据验证失败: ${validation.errors.join(', ')}`)
      }

      const result = await this.db.collection('positions')
        .doc(positionId)
        .update({
          data: position.toDatabase()
        })
      
      return result
    }, 'updatePosition')
  }

  async deletePosition(positionId) {
    return await this.executeWithErrorHandling(async () => {
      if (!positionId) {
        throw new Error('持仓ID不能为空')
      }

      const result = await this.db.collection('positions')
        .doc(positionId)
        .remove()
      
      return result
    }, 'deletePosition')
  }

  // 交易记录
  async addTransaction(transactionData) {
    return await this.executeWithErrorHandling(async () => {
      // 验证交易数据
      this.validateTransactionData(transactionData)

      const result = await this.db.collection('transactions').add({
        data: {
          ...transactionData,
          userId: this.getCurrentUserId(),
          createdAt: new Date()
        }
      })
      
      return result
    }, 'addTransaction')
  }

  async getTransactions(accountId, filters = {}) {
    return await this.executeWithErrorHandling(async () => {
      if (!accountId) {
        throw new Error('账户ID不能为空')
      }

      let query = this.db.collection('transactions')
        .where({
          accountId: accountId,
          userId: this.getCurrentUserId(),
          ...filters
        })
        .orderBy('transactionDate', 'desc')

      const result = await query.get()
      return result.data
    }, 'getTransactions')
  }

  async calculatePnL(transactionId) {
    return await this.executeWithErrorHandling(async () => {
      if (!transactionId) {
        throw new Error('交易ID不能为空')
      }

      // 获取交易记录
      const transaction = await this.db.collection('transactions')
        .doc(transactionId)
        .get()

      if (!transaction.data.length) {
        throw new Error('交易记录不存在')
      }

      const txData = transaction.data[0]
      
      // 这里实现基础的盈亏计算逻辑
      // 实际应用中需要根据具体的交易类型和持仓情况进行复杂计算
      let pnl = 0
      if (txData.type === 'sell') {
        // 卖出交易的盈亏 = (卖出价 - 成本价) * 数量 - 手续费
        pnl = (txData.price - (txData.costPrice || 0)) * txData.quantity - (txData.fees?.total || 0)
      }

      return { pnl, transactionId }
    }, 'calculatePnL')
  }

  // 数据验证方法
  validateAccountData(accountData) {
    if (!accountData.brokerName) {
      throw new Error('券商名称不能为空')
    }
    if (!accountData.accountNumber) {
      throw new Error('账户号码不能为空')
    }
    if (!/^\d{8,20}$/.test(accountData.accountNumber)) {
      throw new Error('账户号码格式不正确，应为8-20位数字')
    }
  }

  validatePositionData(positionData) {
    if (!positionData.symbol) {
      throw new Error('证券代码不能为空')
    }
    if (!/^[0-9]{6}$/.test(positionData.symbol)) {
      throw new Error('证券代码格式不正确，应为6位数字')
    }
    if (!positionData.quantity || positionData.quantity <= 0) {
      throw new Error('持仓数量必须大于0')
    }
    if (!positionData.costPrice || positionData.costPrice <= 0) {
      throw new Error('成本价必须大于0')
    }
  }

  validateTransactionData(transactionData) {
    if (!transactionData.accountId) {
      throw new Error('账户ID不能为空')
    }
    if (!transactionData.symbol) {
      throw new Error('证券代码不能为空')
    }
    if (!['buy', 'sell', 'subscribe', 'redeem'].includes(transactionData.type)) {
      throw new Error('交易类型不正确')
    }
    if (!transactionData.quantity || transactionData.quantity <= 0) {
      throw new Error('交易数量必须大于0')
    }
    if (!transactionData.price || transactionData.price <= 0) {
      throw new Error('交易价格必须大于0')
    }
    if (!transactionData.transactionDate) {
      throw new Error('交易日期不能为空')
    }
  }

  // 工具方法
  getCurrentUserId() {
    // 获取当前用户ID，这里使用微信的openid
    const app = getApp()
    return app.globalData.userInfo?.openid || ''
  }

  // 数据加密
  encryptData(data) {
    try {
      if (!data) return data
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), this.encryptionKey).toString()
      return encrypted
    } catch (error) {
      this.logger.error('数据加密失败', error)
      return data // 加密失败时返回原数据
    }
  }

  // 数据解密
  decryptData(encryptedData) {
    try {
      if (!encryptedData) return encryptedData
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey)
      const decrypted = bytes.toString(CryptoJS.enc.Utf8)
      return JSON.parse(decrypted)
    } catch (error) {
      this.logger.error('数据解密失败', error)
      return encryptedData // 解密失败时返回原数据
    }
  }

  // 批量操作方法
  async batchCreate(collection, dataArray) {
    return await this.executeWithErrorHandling(async () => {
      if (!Array.isArray(dataArray) || dataArray.length === 0) {
        throw new Error('批量数据不能为空')
      }

      const results = []
      for (const data of dataArray) {
        const result = await this.db.collection(collection).add({
          data: {
            ...data,
            userId: this.getCurrentUserId(),
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
        results.push(result)
      }

      return results
    }, 'batchCreate')
  }

  // 数据库连接检查
  async checkConnection() {
    return await this.executeWithErrorHandling(async () => {
      const result = await this.db.collection('users').limit(1).get()
      return { connected: true, timestamp: new Date() }
    }, 'checkConnection')
  }
}

// 导出单例
const dataService = new DataService()
export default dataService