// services/ThemeService.js - 主题管理服务

class ThemeService {
  constructor() {
    this.currentTheme = 'light'
    this.listeners = []
    this.init()
  }

  // 初始化主题服务
  init() {
    try {
      // 从本地存储获取主题设置
      const savedTheme = wx.getStorageSync('theme')
      if (savedTheme) {
        this.currentTheme = savedTheme
      } else {
        // 检测系统主题
        this.detectSystemTheme()
      }
      
      // 应用主题
      this.applyTheme(this.currentTheme)
    } catch (error) {
      console.error('Theme service init failed:', error)
      this.currentTheme = 'light'
      this.applyTheme('light')
    }
  }

  // 检测系统主题
  detectSystemTheme() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      if (systemInfo.theme) {
        this.currentTheme = systemInfo.theme
      }
    } catch (error) {
      console.error('Failed to detect system theme:', error)
    }
  }

  // 获取当前主题
  getCurrentTheme() {
    return this.currentTheme
  }

  // 切换主题
  switchTheme(theme = null) {
    if (theme) {
      this.currentTheme = theme
    } else {
      // 在浅色和深色之间切换
      this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light'
    }

    // 保存到本地存储
    try {
      wx.setStorageSync('theme', this.currentTheme)
    } catch (error) {
      console.error('Failed to save theme:', error)
    }

    // 应用主题
    this.applyTheme(this.currentTheme)

    // 通知监听器
    this.notifyListeners(this.currentTheme)

    return this.currentTheme
  }

  // 应用主题
  applyTheme(theme) {
    try {
      // 设置页面主题属性
      const pages = getCurrentPages()
      pages.forEach(page => {
        if (page.setData) {
          page.setData({
            theme: theme
          })
        }
      })

      // 设置全局主题属性
      if (typeof wx.setPageStyle === 'function') {
        wx.setPageStyle({
          style: {
            'data-theme': theme
          }
        })
      }

      // 更新 tabbar 主题
      this.updateTabBarTheme(theme)

    } catch (error) {
      console.error('Failed to apply theme:', error)
    }
  }

  // 更新 tabbar 主题
  updateTabBarTheme(theme) {
    try {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (typeof currentPage.getTabBar === 'function') {
          const tabBar = currentPage.getTabBar()
          if (tabBar) {
            tabBar.setData({
              theme: theme
            })
          }
        }
      }
    } catch (error) {
      console.error('Failed to update tabbar theme:', error)
    }
  }

  // 添加主题变化监听器
  addThemeListener(callback) {
    if (typeof callback === 'function') {
      this.listeners.push(callback)
    }
  }

  // 移除主题变化监听器
  removeThemeListener(callback) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  // 通知所有监听器
  notifyListeners(theme) {
    this.listeners.forEach(callback => {
      try {
        callback(theme)
      } catch (error) {
        console.error('Theme listener error:', error)
      }
    })
  }

  // 获取主题配置
  getThemeConfig(theme = null) {
    const targetTheme = theme || this.currentTheme
    
    const themes = {
      light: {
        primaryColor: '#007AFF',
        secondaryColor: '#5AC8FA',
        successColor: '#34C759',
        warningColor: '#FF9500',
        errorColor: '#FF3B30',
        textPrimary: '#000000',
        textSecondary: '#666666',
        textTertiary: '#999999',
        backgroundPrimary: '#FFFFFF',
        backgroundSecondary: '#F8F8F8',
        backgroundTertiary: '#F2F2F2',
        borderColor: '#E5E5E5',
        shadowLight: '0 4rpx 20rpx rgba(0, 0, 0, 0.08)',
        shadowMedium: '0 8rpx 30rpx rgba(0, 0, 0, 0.12)'
      },
      dark: {
        primaryColor: '#007AFF',
        secondaryColor: '#5AC8FA',
        successColor: '#34C759',
        warningColor: '#FF9500',
        errorColor: '#FF3B30',
        textPrimary: '#FFFFFF',
        textSecondary: '#CCCCCC',
        textTertiary: '#999999',
        backgroundPrimary: '#1C1C1E',
        backgroundSecondary: '#2C2C2E',
        backgroundTertiary: '#3A3A3C',
        borderColor: '#48484A',
        shadowLight: '0 4rpx 20rpx rgba(0, 0, 0, 0.3)',
        shadowMedium: '0 8rpx 30rpx rgba(0, 0, 0, 0.4)'
      }
    }

    return themes[targetTheme] || themes.light
  }

  // 监听系统主题变化
  onSystemThemeChange(callback) {
    try {
      wx.onThemeChange((res) => {
        if (res.theme !== this.currentTheme) {
          this.switchTheme(res.theme)
          if (callback) {
            callback(res.theme)
          }
        }
      })
    } catch (error) {
      console.error('Failed to listen system theme change:', error)
    }
  }

  // 获取主题显示名称
  getThemeDisplayName(theme = null) {
    const targetTheme = theme || this.currentTheme
    const names = {
      light: '浅色模式',
      dark: '深色模式',
      auto: '跟随系统'
    }
    return names[targetTheme] || '未知主题'
  }

  // 获取可用主题列表
  getAvailableThemes() {
    return [
      { key: 'light', name: '浅色模式', icon: '☀️' },
      { key: 'dark', name: '深色模式', icon: '🌙' },
      { key: 'auto', name: '跟随系统', icon: '🔄' }
    ]
  }
}

// 创建单例实例
const themeService = new ThemeService()

module.exports = themeService