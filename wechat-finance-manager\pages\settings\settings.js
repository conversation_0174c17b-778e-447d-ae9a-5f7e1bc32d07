// pages/settings/settings.js
const ThemeService = require('../../services/ThemeService')
const pageTransition = require('../../utils/pageTransition')

Page({
  data: {
    theme: 'light',
    userInfo: null,
    autoRefresh: true,
    notifications: true,
    pageTransition: ''
  },

  onLoad() {
    console.log('Settings page loaded')
    this.initTheme()
    this.loadSettings()
  },

  onShow() {
    this.updateTabBar()
  },

  onUnload() {
    if (this.themeChangeHandler) {
      ThemeService.removeThemeListener(this.themeChangeHandler)
    }
  },

  // 初始化主题
  initTheme() {
    try {
      const theme = ThemeService.getCurrentTheme()
      this.setData({ theme })
      
      // 添加主题变化监听器
      this.themeChangeHandler = (newTheme) => {
        this.setData({ theme: newTheme })
      }
      ThemeService.addThemeListener(this.themeChangeHandler)
    } catch (e) {
      console.error('Failed to load theme:', e)
    }
  },

  // 加载设置
  loadSettings() {
    try {
      const app = getApp()
      const autoRefresh = wx.getStorageSync('autoRefresh') !== false
      const notifications = wx.getStorageSync('notifications') !== false
      
      this.setData({
        userInfo: app.globalData.userInfo,
        autoRefresh: autoRefresh,
        notifications: notifications
      })
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  },

  // 主题变化处理
  onThemeChange(e) {
    const { theme } = e.detail
    console.log('Theme changed to:', theme)
  },

  // 自动刷新设置变化
  onAutoRefreshChange(e) {
    const autoRefresh = e.detail.value
    this.setData({ autoRefresh })
    
    try {
      wx.setStorageSync('autoRefresh', autoRefresh)
      wx.showToast({
        title: autoRefresh ? '已开启自动刷新' : '已关闭自动刷新',
        icon: 'success'
      })
    } catch (error) {
      console.error('Failed to save auto refresh setting:', error)
    }
  },

  // 通知设置变化
  onNotificationChange(e) {
    const notifications = e.detail.value
    this.setData({ notifications })
    
    try {
      wx.setStorageSync('notifications', notifications)
      wx.showToast({
        title: notifications ? '已开启消息通知' : '已关闭消息通知',
        icon: 'success'
      })
    } catch (error) {
      console.error('Failed to save notification setting:', error)
    }
  },

  // 跳转到关于页面
  navigateToAbout() {
    wx.showModal({
      title: '关于理财管家',
      content: '版本：1.0.0\n\n一个简洁易用的个人投资管理工具，帮助您更好地管理证券和基金投资。',
      showCancel: false,
      confirmText: '确定'
    })
  },

  // 导出数据
  exportData() {
    wx.showLoading({
      title: '准备导出...'
    })

    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    }, 1000)
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除本地缓存数据吗？这不会影响您的账户数据。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清理中...'
          })

          setTimeout(() => {
            wx.hideLoading()
            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
          }, 1000)
        }
      }
    })
  },

  // 更新 tabbar 状态
  updateTabBar() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      })
    }
  }
})