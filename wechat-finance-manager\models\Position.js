/**
 * 持仓数据模型
 * 负责证券和基金持仓的数据结构定义、验证和管理
 */

class Position {
  constructor(data = {}) {
    this._id = data._id || null;
    this.accountId = data.accountId || '';
    this.userId = data.userId || '';
    this.symbol = data.symbol || '';
    this.name = data.name || '';
    this.type = data.type || 'stock'; // stock/fund/bond
    this.quantity = data.quantity || 0;
    this.costPrice = data.costPrice || 0;
    this.currentPrice = data.currentPrice || 0;
    this.marketValue = data.marketValue || 0;
    this.unrealizedPnL = data.unrealizedPnL || 0;
    this.unrealizedPnLPercent = data.unrealizedPnLPercent || 0;
    this.dailyChange = data.dailyChange || 0;
    this.dailyChangePercent = data.dailyChangePercent || 0;
    this.totalCost = data.totalCost || 0;
    this.availableQuantity = data.availableQuantity || 0;
    this.frozenQuantity = data.frozenQuantity || 0;
    this.dividendAmount = data.dividendAmount || 0;
    this.lastTradeDate = data.lastTradeDate || null;
    this.priceUpdateTime = data.priceUpdateTime || null;
    this.isActive = data.isActive !== undefined ? data.isActive : true;
    this.notes = data.notes || '';
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  /**
   * 验证持仓数据
   * @returns {Object} 验证结果 {isValid: boolean, errors: Array}
   */
  validate() {
    const errors = [];

    // 验证账户ID
    if (!this.accountId || typeof this.accountId !== 'string') {
      errors.push('账户ID是必填字段且必须为字符串');
    }

    // 验证用户ID
    if (!this.userId || typeof this.userId !== 'string') {
      errors.push('用户ID是必填字段且必须为字符串');
    }

    // 验证证券代码
    if (!this.symbol || typeof this.symbol !== 'string') {
      errors.push('证券代码是必填字段且必须为字符串');
    }

    // 验证证券代码格式
    if (this.symbol && !this.isValidSymbol(this.symbol)) {
      errors.push('证券代码格式无效');
    }

    // 验证证券名称
    if (this.name && typeof this.name !== 'string') {
      errors.push('证券名称必须为字符串');
    }

    if (this.name && this.name.length > 100) {
      errors.push('证券名称长度不能超过100个字符');
    }

    // 验证证券类型
    if (!['stock', 'fund', 'bond'].includes(this.type)) {
      errors.push('证券类型只能是stock、fund或bond');
    }

    // 验证数量
    if (typeof this.quantity !== 'number' || this.quantity < 0) {
      errors.push('持仓数量必须为非负数');
    }

    // 验证成本价
    if (typeof this.costPrice !== 'number' || this.costPrice < 0) {
      errors.push('成本价必须为非负数');
    }

    // 验证当前价格
    if (typeof this.currentPrice !== 'number' || this.currentPrice < 0) {
      errors.push('当前价格必须为非负数');
    }

    // 验证市值
    if (typeof this.marketValue !== 'number' || this.marketValue < 0) {
      errors.push('市值必须为非负数');
    }

    // 验证数值字段
    if (typeof this.unrealizedPnL !== 'number') {
      errors.push('未实现盈亏必须为数字');
    }

    if (typeof this.unrealizedPnLPercent !== 'number') {
      errors.push('未实现盈亏百分比必须为数字');
    }

    if (typeof this.dailyChange !== 'number') {
      errors.push('日变化必须为数字');
    }

    if (typeof this.dailyChangePercent !== 'number') {
      errors.push('日变化百分比必须为数字');
    }

    // 验证可用数量和冻结数量
    if (typeof this.availableQuantity !== 'number' || this.availableQuantity < 0) {
      errors.push('可用数量必须为非负数');
    }

    if (typeof this.frozenQuantity !== 'number' || this.frozenQuantity < 0) {
      errors.push('冻结数量必须为非负数');
    }

    // 验证数量逻辑关系
    if (this.availableQuantity + this.frozenQuantity > this.quantity) {
      errors.push('可用数量加冻结数量不能超过总持仓数量');
    }

    // 验证布尔字段
    if (typeof this.isActive !== 'boolean') {
      errors.push('激活状态必须为布尔值');
    }

    // 验证备注
    if (this.notes && typeof this.notes !== 'string') {
      errors.push('备注必须为字符串');
    }

    if (this.notes && this.notes.length > 500) {
      errors.push('备注长度不能超过500个字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证证券代码格式
   * @param {string} symbol 证券代码
   * @returns {boolean} 是否有效
   */
  isValidSymbol(symbol) {
    // A股股票代码：6位数字
    const stockPattern = /^[0-9]{6}$/;
    // 基金代码：6位数字
    const fundPattern = /^[0-9]{6}$/;
    // 港股代码：5位数字
    const hkStockPattern = /^[0-9]{5}$/;
    // 美股代码：1-5位字母
    const usStockPattern = /^[A-Z]{1,5}$/;

    return stockPattern.test(symbol) || 
           fundPattern.test(symbol) || 
           hkStockPattern.test(symbol) || 
           usStockPattern.test(symbol);
  }

  /**
   * 转换为数据库存储格式
   * @returns {Object} 数据库存储对象
   */
  toDatabase() {
    const data = {
      accountId: this.accountId,
      userId: this.userId,
      symbol: this.symbol,
      name: this.name,
      type: this.type,
      quantity: this.quantity,
      costPrice: this.costPrice,
      currentPrice: this.currentPrice,
      marketValue: this.marketValue,
      unrealizedPnL: this.unrealizedPnL,
      unrealizedPnLPercent: this.unrealizedPnLPercent,
      dailyChange: this.dailyChange,
      dailyChangePercent: this.dailyChangePercent,
      totalCost: this.totalCost,
      availableQuantity: this.availableQuantity,
      frozenQuantity: this.frozenQuantity,
      dividendAmount: this.dividendAmount,
      lastTradeDate: this.lastTradeDate,
      priceUpdateTime: this.priceUpdateTime,
      isActive: this.isActive,
      notes: this.notes,
      updatedAt: new Date()
    };

    // 如果是新持仓，添加创建时间
    if (!this._id) {
      data.createdAt = new Date();
    }

    return data;
  }

  /**
   * 从数据库数据创建持仓实例
   * @param {Object} dbData 数据库数据
   * @returns {Position} 持仓实例
   */
  static fromDatabase(dbData) {
    return new Position({
      _id: dbData._id,
      accountId: dbData.accountId,
      userId: dbData.userId,
      symbol: dbData.symbol,
      name: dbData.name,
      type: dbData.type,
      quantity: dbData.quantity,
      costPrice: dbData.costPrice,
      currentPrice: dbData.currentPrice,
      marketValue: dbData.marketValue,
      unrealizedPnL: dbData.unrealizedPnL,
      unrealizedPnLPercent: dbData.unrealizedPnLPercent,
      dailyChange: dbData.dailyChange,
      dailyChangePercent: dbData.dailyChangePercent,
      totalCost: dbData.totalCost,
      availableQuantity: dbData.availableQuantity,
      frozenQuantity: dbData.frozenQuantity,
      dividendAmount: dbData.dividendAmount,
      lastTradeDate: dbData.lastTradeDate,
      priceUpdateTime: dbData.priceUpdateTime,
      isActive: dbData.isActive,
      notes: dbData.notes,
      createdAt: dbData.createdAt,
      updatedAt: dbData.updatedAt
    });
  }

  /**
   * 更新持仓信息
   * @param {Object} updateData 更新数据
   */
  update(updateData) {
    if (updateData.symbol !== undefined) {
      this.symbol = updateData.symbol;
    }
    if (updateData.name !== undefined) {
      this.name = updateData.name;
    }
    if (updateData.type !== undefined) {
      this.type = updateData.type;
    }
    if (updateData.quantity !== undefined) {
      this.quantity = updateData.quantity;
    }
    if (updateData.costPrice !== undefined) {
      this.costPrice = updateData.costPrice;
    }
    if (updateData.currentPrice !== undefined) {
      this.currentPrice = updateData.currentPrice;
    }
    if (updateData.availableQuantity !== undefined) {
      this.availableQuantity = updateData.availableQuantity;
    }
    if (updateData.frozenQuantity !== undefined) {
      this.frozenQuantity = updateData.frozenQuantity;
    }
    if (updateData.dividendAmount !== undefined) {
      this.dividendAmount = updateData.dividendAmount;
    }
    if (updateData.lastTradeDate !== undefined) {
      this.lastTradeDate = updateData.lastTradeDate;
    }
    if (updateData.isActive !== undefined) {
      this.isActive = updateData.isActive;
    }
    if (updateData.notes !== undefined) {
      this.notes = updateData.notes;
    }

    // 重新计算相关数值
    this.recalculate();
    this.updatedAt = new Date();
  }

  /**
   * 重新计算持仓相关数值
   */
  recalculate() {
    // 计算总成本
    this.totalCost = this.quantity * this.costPrice;
    
    // 计算市值
    this.marketValue = this.quantity * this.currentPrice;
    
    // 计算未实现盈亏
    this.unrealizedPnL = this.marketValue - this.totalCost;
    
    // 计算未实现盈亏百分比
    if (this.totalCost > 0) {
      this.unrealizedPnLPercent = (this.unrealizedPnL / this.totalCost) * 100;
    } else {
      this.unrealizedPnLPercent = 0;
    }
  }

  /**
   * 更新价格信息
   * @param {number} currentPrice 当前价格
   * @param {number} dailyChange 日变化
   * @param {number} dailyChangePercent 日变化百分比
   */
  updatePrice(currentPrice, dailyChange = 0, dailyChangePercent = 0) {
    this.currentPrice = currentPrice;
    this.dailyChange = dailyChange;
    this.dailyChangePercent = dailyChangePercent;
    this.priceUpdateTime = new Date();
    
    // 重新计算相关数值
    this.recalculate();
    this.updatedAt = new Date();
  }

  /**
   * 获取持仓显示信息
   * @returns {Object} 持仓显示信息
   */
  getDisplayInfo() {
    return {
      _id: this._id,
      symbol: this.symbol,
      name: this.name || this.symbol,
      type: this.type,
      typeName: this.getTypeName(),
      quantity: this.quantity,
      costPrice: this.costPrice,
      currentPrice: this.currentPrice,
      marketValue: this.marketValue,
      totalCost: this.totalCost,
      unrealizedPnL: this.unrealizedPnL,
      unrealizedPnLPercent: this.unrealizedPnLPercent,
      dailyChange: this.dailyChange,
      dailyChangePercent: this.dailyChangePercent,
      availableQuantity: this.availableQuantity,
      frozenQuantity: this.frozenQuantity,
      dividendAmount: this.dividendAmount,
      profitStatus: this.getProfitStatus(),
      priceUpdateTime: this.priceUpdateTime,
      isActive: this.isActive,
      notes: this.notes
    };
  }

  /**
   * 获取证券类型中文名称
   * @returns {string} 证券类型中文名称
   */
  getTypeName() {
    const typeNames = {
      stock: '股票',
      fund: '基金',
      bond: '债券'
    };
    return typeNames[this.type] || '未知类型';
  }

  /**
   * 获取盈亏状态
   * @returns {string} 盈亏状态 (profit/loss/break-even)
   */
  getProfitStatus() {
    if (this.unrealizedPnL > 0) {
      return 'profit';
    } else if (this.unrealizedPnL < 0) {
      return 'loss';
    } else {
      return 'break-even';
    }
  }

  /**
   * 获取持仓权重（在账户中的占比）
   * @param {number} totalAccountValue 账户总价值
   * @returns {number} 持仓权重百分比
   */
  getWeight(totalAccountValue) {
    if (totalAccountValue <= 0) {
      return 0;
    }
    return (this.marketValue / totalAccountValue) * 100;
  }

  /**
   * 检查是否可以卖出指定数量
   * @param {number} sellQuantity 卖出数量
   * @returns {boolean} 是否可以卖出
   */
  canSell(sellQuantity) {
    return this.availableQuantity >= sellQuantity;
  }

  /**
   * 冻结指定数量的持仓
   * @param {number} freezeQuantity 冻结数量
   * @returns {boolean} 是否成功冻结
   */
  freeze(freezeQuantity) {
    if (this.availableQuantity >= freezeQuantity) {
      this.availableQuantity -= freezeQuantity;
      this.frozenQuantity += freezeQuantity;
      this.updatedAt = new Date();
      return true;
    }
    return false;
  }

  /**
   * 解冻指定数量的持仓
   * @param {number} unfreezeQuantity 解冻数量
   * @returns {boolean} 是否成功解冻
   */
  unfreeze(unfreezeQuantity) {
    if (this.frozenQuantity >= unfreezeQuantity) {
      this.frozenQuantity -= unfreezeQuantity;
      this.availableQuantity += unfreezeQuantity;
      this.updatedAt = new Date();
      return true;
    }
    return false;
  }

  /**
   * 检查持仓权限
   * @param {string} userId 用户ID
   * @returns {boolean} 是否有权限
   */
  checkPermission(userId) {
    return this.userId === userId;
  }
}

module.exports = Position;