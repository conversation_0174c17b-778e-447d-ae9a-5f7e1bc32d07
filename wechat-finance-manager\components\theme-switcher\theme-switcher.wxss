/* components/theme-switcher/theme-switcher.wxss */
.theme-switcher {
  width: 100%;
}

.switcher-container {
  display: flex;
  background: var(--background-primary);
  border-radius: var(--border-radius);
  padding: 8rpx;
  box-shadow: var(--shadow-light);
  overflow: hidden;
}

.theme-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border-radius: var(--border-radius-small);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
}

.theme-option:active {
  transform: scale(0.95);
}

.theme-option.active {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.theme-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.theme-option.active .theme-icon {
  transform: scale(1.1);
}

.theme-name {
  font-size: 24rpx;
  font-weight: 500;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.theme-option.active .theme-name {
  color: white;
  font-weight: 600;
}

.theme-indicator {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
}

.indicator-dot {
  display: inline-block;
  width: 12rpx;
  height: 12rpx;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 快速切换按钮 */
.quick-switch {
  position: fixed;
  top: 200rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: var(--background-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-medium);
  z-index: 999;
  transition: all 0.3s ease;
}

.quick-switch:active {
  transform: scale(0.9);
}

.switch-icon {
  font-size: 36rpx;
  transition: all 0.3s ease;
}

.quick-switch:active .switch-icon {
  transform: rotate(180deg);
}

/* 深色模式适配 */
.theme-switcher.dark .switcher-container {
  background: var(--background-primary);
}

.theme-switcher.dark .quick-switch {
  background: var(--background-primary);
  border: 1rpx solid var(--border-color);
}

/* 动画效果 */
@keyframes themeSwitch {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.theme-option.switching {
  animation: themeSwitch 0.3s ease;
}