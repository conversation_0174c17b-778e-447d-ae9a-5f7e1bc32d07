<!--pages/add-account/add-account.wxml-->
<view class="container">
  <form bindsubmit="submitForm">
    <!-- 账户类型选择 -->
    <view class="form-group">
      <label class="form-label">账户类型</label>
      <radio-group class="radio-group" bindchange="onAccountTypeChange">
        <label class="radio-item">
          <radio value="securities" checked="{{accountType === 'securities'}}" />
          <text class="radio-text">证券账户</text>
        </label>
        <label class="radio-item">
          <radio value="fund" checked="{{accountType === 'fund'}}" />
          <text class="radio-text">基金账户</text>
        </label>
      </radio-group>
    </view>

    <!-- 券商选择 -->
    <view class="form-group">
      <label class="form-label">券商名称</label>
      <view class="picker-wrapper" bindtap="showBrokerPicker">
        <input class="form-input picker-input" 
               placeholder="请选择券商" 
               value="{{brokerName}}"
               disabled />
        <text class="picker-arrow">></text>
      </view>
      <view class="error-text" wx:if="{{errors.brokerName}}">{{errors.brokerName}}</view>
    </view>

    <!-- 账户号码输入 -->
    <view class="form-group">
      <label class="form-label">账户号码</label>
      <input class="form-input {{errors.accountNumber ? 'error' : ''}}" 
             placeholder="请输入8-20位数字账户号码" 
             value="{{accountNumber}}"
             type="number"
             maxlength="20"
             bindinput="onAccountNumberInput" />
      <view class="error-text" wx:if="{{errors.accountNumber}}">{{errors.accountNumber}}</view>
    </view>

    <!-- 提交按钮 -->
    <button class="btn btn-primary btn-block" 
            form-type="submit"
            loading="{{loading}}"
            disabled="{{loading}}">
      {{mode === 'edit' ? '更新账户' : '添加账户'}}
    </button>

    <!-- 删除按钮（编辑模式） -->
    <button class="btn btn-danger btn-block" 
            wx:if="{{mode === 'edit'}}"
            bindtap="deleteAccount"
            disabled="{{loading}}">
      删除账户
    </button>
  </form>

  <!-- 券商选择器 -->
  <picker-view class="broker-picker {{showBrokerPicker ? 'show' : ''}}" 
               wx:if="{{showBrokerPicker}}"
               bindchange="onBrokerChange">
    <view class="picker-mask" bindtap="onBrokerCancel"></view>
    <view class="picker-content">
      <view class="picker-header">
        <text class="picker-cancel" bindtap="onBrokerCancel">取消</text>
        <text class="picker-title">选择券商</text>
        <text class="picker-confirm" bindtap="onBrokerChange" data-value="{{brokerIndex}}">确定</text>
      </view>
      <picker-view-column class="picker-column">
        <view class="picker-item {{index === brokerIndex ? 'selected' : ''}}" 
              wx:for="{{brokerList}}" 
              wx:key="*this"
              data-index="{{index}}"
              bindtap="onBrokerChange"
              data-value="{{index}}">
          {{item}}
        </view>
      </picker-view-column>
    </view>
  </picker-view>
</view>