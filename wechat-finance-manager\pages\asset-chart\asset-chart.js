// pages/asset-chart/asset-chart.js
import { AssetCalculationService } from '../../services/AssetCalculationService'
import { ThemeService } from '../../services/ThemeService'
import { generateDateRange } from '../../utils/chartUtils'

Page({
  data: {
    // 图表数据
    chartData: [],
    loading: true,
    
    // 时间范围选项
    timeRanges: [
      { key: 'month', label: '本月', active: true },
      { key: 'quarter', label: '季度', active: false },
      { key: 'halfYear', label: '半年', active: false },
      { key: 'year', label: '今年', active: false },
      { key: 'all', label: '历史', active: false }
    ],
    currentTimeRange: 'month',
    
    // 主题
    theme: 'light',
    
    // 统计信息
    totalAssets: 0,
    dailyChange: 0,
    dailyChangePercent: 0,
    
    // 图表配置
    chartHeight: 400
  },

  onLoad(options) {
    // 获取当前主题
    const theme = ThemeService.getCurrentTheme()
    this.setData({ theme })
    
    // 加载图表数据
    this.loadChartData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadChartData()
  },

  onPullDownRefresh() {
    this.loadChartData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载图表数据
  async loadChartData() {
    try {
      this.setData({ loading: true })
      
      const { currentTimeRange } = this.data
      const chartData = await this.getAssetHistoryData(currentTimeRange)
      
      // 计算统计信息
      const stats = this.calculateStats(chartData)
      
      this.setData({
        chartData,
        totalAssets: stats.totalAssets,
        dailyChange: stats.dailyChange,
        dailyChangePercent: stats.dailyChangePercent,
        loading: false
      })
      
    } catch (error) {
      console.error('加载图表数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  // 获取资产历史数据
  async getAssetHistoryData(timeRange) {
    // 生成日期范围
    const dateRange = generateDateRange(timeRange)
    const chartData = []
    
    // 获取用户ID
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.openid) {
      throw new Error('用户未登录')
    }
    
    try {
      // 调用云函数获取历史资产数据
      const result = await wx.cloud.callFunction({
        name: 'getAssetHistory',
        data: {
          userId: userInfo.openid,
          timeRange,
          dateRange
        }
      })
      
      if (result.result && result.result.success) {
        return result.result.data.map(item => ({
          date: this.formatDateLabel(item.date, timeRange),
          value: item.totalValue || 0,
          timestamp: item.date
        }))
      } else {
        // 如果没有历史数据，生成模拟数据用于演示
        return this.generateMockData(dateRange, timeRange)
      }
      
    } catch (error) {
      console.error('获取历史数据失败:', error)
      // 生成模拟数据
      return this.generateMockData(dateRange, timeRange)
    }
  },

  // 生成模拟数据（用于演示）
  generateMockData(dateRange, timeRange) {
    const baseValue = 50000 // 基础资产价值
    const data = []
    
    dateRange.forEach((date, index) => {
      // 生成随机波动
      const randomFactor = 0.95 + Math.random() * 0.1 // 0.95-1.05的随机因子
      const trendFactor = 1 + (index * 0.002) // 轻微上升趋势
      const value = Math.round(baseValue * randomFactor * trendFactor)
      
      data.push({
        date: date,
        value: value,
        timestamp: new Date().getTime() - (dateRange.length - index - 1) * 24 * 60 * 60 * 1000
      })
    })
    
    return data
  },

  // 格式化日期标签
  formatDateLabel(timestamp, timeRange) {
    const date = new Date(timestamp)
    const month = date.getMonth() + 1
    const day = date.getDate()
    
    switch (timeRange) {
      case 'month':
        return `${month}/${day}`
      case 'quarter':
      case 'halfYear':
        return `${month}/${day}`
      case 'year':
        return `${month}月`
      case 'all':
        return `${date.getFullYear()}/${month}`
      default:
        return `${month}/${day}`
    }
  },

  // 计算统计信息
  calculateStats(chartData) {
    if (!chartData || chartData.length === 0) {
      return {
        totalAssets: 0,
        dailyChange: 0,
        dailyChangePercent: 0
      }
    }
    
    const latestData = chartData[chartData.length - 1]
    const previousData = chartData.length > 1 ? chartData[chartData.length - 2] : latestData
    
    const totalAssets = latestData.value
    const dailyChange = totalAssets - previousData.value
    const dailyChangePercent = previousData.value !== 0 ? (dailyChange / previousData.value) : 0
    
    return {
      totalAssets,
      dailyChange,
      dailyChangePercent
    }
  },

  // 切换时间范围
  onTimeRangeChange(e) {
    const { range } = e.currentTarget.dataset
    
    // 更新时间范围状态
    const timeRanges = this.data.timeRanges.map(item => ({
      ...item,
      active: item.key === range
    }))
    
    this.setData({
      timeRanges,
      currentTimeRange: range
    })
    
    // 重新加载数据
    this.loadChartData()
  },

  // 图表准备完成
  onChartReady(e) {
    console.log('图表准备完成:', e.detail)
  },

  // 图表更新完成
  onChartUpdate(e) {
    console.log('图表更新完成:', e.detail)
  },

  // 图表点击事件
  onChartClick(e) {
    console.log('图表点击:', e.detail)
  },

  // 图表错误处理
  onChartError(e) {
    console.error('图表错误:', e.detail)
    wx.showToast({
      title: '图表加载失败',
      icon: 'none'
    })
  },

  // 导出图表
  async onExportChart() {
    try {
      wx.showLoading({ title: '导出中...' })
      
      const chartComponent = this.selectComponent('#asset-chart')
      const imagePath = await chartComponent.exportImage()
      
      wx.hideLoading()
      
      // 保存到相册
      wx.saveImageToPhotosAlbum({
        filePath: imagePath,
        success: () => {
          wx.showToast({
            title: '已保存到相册',
            icon: 'success'
          })
        },
        fail: (error) => {
          console.error('保存失败:', error)
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
      })
      
    } catch (error) {
      wx.hideLoading()
      console.error('导出图表失败:', error)
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      })
    }
  },

  // 分享图表
  onShareChart() {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '我的资产变化图表',
      path: '/pages/asset-chart/asset-chart',
      imageUrl: '/images/share-chart.png'
    }
  }
})