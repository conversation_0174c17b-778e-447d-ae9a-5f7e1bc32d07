<!--pages/account-detail/account-detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 账户详情内容 -->
  <view class="content" wx:if="{{!loading && account}}">
    <!-- 账户信息卡片 -->
    <view class="account-info-card">
      <view class="account-header">
        <view class="broker-info">
          <view class="broker-name">{{account.brokerName}}</view>
          <view class="account-number">{{account.accountNumber}}</view>
        </view>
        <view class="account-type">
          <text class="type-badge">{{account.accountType === 'securities' ? '证券' : '基金'}}</text>
        </view>
      </view>
      
      <view class="account-stats">
        <view class="stat-item">
          <view class="stat-label">总资产</view>
          <view class="stat-value">¥{{formatValue(statistics.totalValue)}}</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">总成本</view>
          <view class="stat-value">¥{{formatValue(statistics.totalCost)}}</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">盈亏</view>
          <view class="stat-value {{getPnLColorClass(statistics.totalPnL)}}">
            {{statistics.totalPnL >= 0 ? '+' : ''}}¥{{formatValue(statistics.totalPnL)}}
          </view>
          <view class="stat-percent {{getPnLColorClass(statistics.totalPnL)}}">
            {{statistics.totalPnLPercent >= 0 ? '+' : ''}}{{formatPercent(statistics.totalPnLPercent)}}%
          </view>
        </view>
      </view>
    </view>

    <!-- 持仓列表 -->
    <view class="positions-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-text">持仓列表</text>
          <text class="position-count">({{filteredPositions.length}}/{{positions.length}})</text>
        </view>
        <view class="header-actions">
          <view class="action-btn-small" bindtap="importPositions">
            <text class="action-icon">📥</text>
            <text class="action-text">导入</text>
          </view>
          <view class="action-btn-small" bindtap="toggleBatchMode">
            <text class="action-icon">{{batchMode ? '✓' : '📋'}}</text>
            <text class="action-text">{{batchMode ? '完成' : '管理'}}</text>
          </view>
          <view class="add-position-btn" bindtap="addPosition">
            <text class="add-icon">+</text>
          </view>
        </view>
      </view>

      <!-- 筛选和排序 -->
      <view class="filter-section">
        <scroll-view class="filter-scroll" scroll-x>
          <view class="filter-items">
            <!-- 类型筛选 -->
            <picker class="filter-picker" mode="selector" range="{{typeOptions}}" range-key="label" bindchange="onFilterTypeChange">
              <view class="filter-item {{filterOptions.type !== 'all' ? 'active' : ''}}">
                <text class="filter-text">{{getFilterTypeText()}}</text>
                <text class="filter-arrow">▼</text>
              </view>
            </picker>
            
            <!-- 状态筛选 -->
            <picker class="filter-picker" mode="selector" range="{{statusOptions}}" range-key="label" bindchange="onFilterStatusChange">
              <view class="filter-item {{filterOptions.status !== 'all' ? 'active' : ''}}">
                <text class="filter-text">{{getFilterStatusText()}}</text>
                <text class="filter-arrow">▼</text>
              </view>
            </picker>
            
            <!-- 排序方式 -->
            <picker class="filter-picker" mode="selector" range="{{sortOptions}}" range-key="label" bindchange="onSortByChange">
              <view class="filter-item">
                <text class="filter-text">{{getSortByText()}}</text>
                <text class="filter-arrow">▼</text>
              </view>
            </picker>
            
            <!-- 排序顺序 -->
            <view class="filter-item" bindtap="toggleSortOrder">
              <text class="filter-text">{{filterOptions.sortOrder === 'desc' ? '↓' : '↑'}}</text>
            </view>
            
            <!-- 重置筛选 -->
            <view class="filter-item reset" bindtap="resetFilter">
              <text class="filter-text">重置</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 批量操作栏 -->
      <view wx:if="{{batchMode}}" class="batch-toolbar">
        <view class="batch-actions">
          <view class="batch-select">
            <text class="select-text" bindtap="toggleSelectAll">
              {{selectedPositions.length === filteredPositions.length ? '取消全选' : '全选'}}
            </text>
            <text class="select-count">({{selectedPositions.length}}/{{filteredPositions.length}})</text>
          </view>
          <view class="batch-buttons">
            <button class="batch-btn export" bindtap="batchExport" disabled="{{selectedPositions.length === 0}}">
              导出
            </button>
            <button class="batch-btn delete" bindtap="batchDelete" disabled="{{selectedPositions.length === 0}}">
              删除
            </button>
          </view>
        </view>
      </view>

      <!-- 持仓列表内容 -->
      <view class="positions-list" wx:if="{{filteredPositions.length > 0}}">
        <view class="position-item {{batchMode ? 'batch-mode' : ''}} {{isPositionSelected(item._id) ? 'selected' : ''}}" 
              wx:for="{{filteredPositions}}" 
              wx:key="_id"
              bindtap="{{batchMode ? 'togglePositionSelection' : 'viewPositionDetail'}}"
              data-id="{{item._id}}">
          
          <!-- 批量选择框 -->
          <view wx:if="{{batchMode}}" class="selection-checkbox">
            <view class="checkbox {{isPositionSelected(item._id) ? 'checked' : ''}}">
              <text wx:if="{{isPositionSelected(item._id)}}" class="check-icon">✓</text>
            </view>
          </view>
          <view class="position-info">
            <view class="position-header">
              <view class="symbol-name">
                <text class="symbol">{{item.symbol}}</text>
                <text class="name">{{item.name}}</text>
              </view>
              <view wx:if="{{!batchMode}}" class="position-actions">
                <text class="action-btn" 
                      bindtap="editPosition" 
                      data-position="{{item}}"
                      catchtap="">编辑</text>
                <text class="action-btn delete" 
                      bindtap="deletePosition" 
                      data-id="{{item._id}}"
                      data-name="{{item.name}}"
                      catchtap="">删除</text>
              </view>
            </view>
            
            <view class="position-details">
              <view class="detail-row">
                <text class="detail-label">持仓:</text>
                <text class="detail-value">{{item.quantity}}股</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">成本:</text>
                <text class="detail-value">¥{{formatValue(item.costPrice)}}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">现价:</text>
                <text class="detail-value">¥{{formatValue(item.currentPrice)}}</text>
              </view>
            </view>
          </view>
          
          <view class="position-value">
            <view class="market-value">¥{{formatValue(item.marketValue || item.quantity * item.currentPrice)}}</view>
            <view class="unrealized-pnl {{getPnLColorClass(item.unrealizedPnL)}}">
              {{item.unrealizedPnL >= 0 ? '+' : ''}}¥{{formatValue(item.unrealizedPnL)}}
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-positions" wx:if="{{positions.length === 0}}">
        <view class="empty-icon">📊</view>
        <view class="empty-text">暂无持仓</view>
        <view class="empty-desc">点击右上角"+"添加持仓</view>
      </view>
      
      <!-- 筛选后无结果 -->
      <view class="empty-positions" wx:elif="{{filteredPositions.length === 0}}">
        <view class="empty-icon">🔍</view>
        <view class="empty-text">无匹配结果</view>
        <view class="empty-desc">尝试调整筛选条件</view>
      </view>
    </view>
  </view>

  <!-- 刷新提示 -->
  <view class="refresh-hint" wx:if="{{refreshing}}">
    <text class="refresh-text">刷新中...</text>
  </view>
</view>