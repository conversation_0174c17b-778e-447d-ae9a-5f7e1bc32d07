/* components/asset-overview/asset-overview.wxss */
.asset-overview-container {
  padding: 20rpx;
}

.asset-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.asset-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
}

.asset-card.dark {
  background: #2c2c2e;
  color: #ffffff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.asset-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.asset-card.dark .asset-title {
  color: #ffffff;
}

.asset-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.last-updated {
  font-size: 22rpx;
  color: #8e8e93;
  font-weight: 400;
}

.asset-card.dark .last-updated {
  color: #98989d;
}

.refresh-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: #f5f5f7;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.asset-card.dark .refresh-btn {
  background: #48484a;
}

.refresh-btn:active {
  transform: scale(0.95);
  background: #e5e5ea;
}

.asset-card.dark .refresh-btn:active {
  background: #636366;
}

.refresh-icon {
  font-size: 32rpx;
  color: #666666;
  font-weight: bold;
}

.asset-card.dark .refresh-icon {
  color: #ffffff;
}

.asset-content {
  text-align: center;
}

.asset-amount {
  font-size: 64rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 16rpx;
  letter-spacing: -1rpx;
}

.asset-card.dark .asset-amount {
  color: #ffffff;
}

.asset-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.change-amount {
  font-size: 32rpx;
  font-weight: 600;
}

.change-percent {
  font-size: 28rpx;
  font-weight: 500;
}

.asset-change.positive .change-amount,
.asset-change.positive .change-percent {
  color: #34c759;
}

.asset-change.negative .change-amount,
.asset-change.negative .change-percent {
  color: #ff3b30;
}

.asset-subtitle {
  font-size: 24rpx;
  color: #8e8e93;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.asset-card.dark .asset-subtitle {
  color: #98989d;
}

.asset-total-pnl {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-top: 1rpx solid #f2f2f7;
  margin-top: 16rpx;
}

.asset-card.dark .asset-total-pnl {
  border-top-color: #48484a;
}

.pnl-label {
  font-size: 26rpx;
  color: #8e8e93;
  font-weight: 500;
}

.asset-card.dark .pnl-label {
  color: #98989d;
}

.pnl-value {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.pnl-amount {
  font-size: 28rpx;
  font-weight: 600;
}

.pnl-percent {
  font-size: 24rpx;
  font-weight: 500;
}

.pnl-value.positive .pnl-amount,
.pnl-value.positive .pnl-percent {
  color: #34c759;
}

.pnl-value.negative .pnl-amount,
.pnl-value.negative .pnl-percent {
  color: #ff3b30;
}

.asset-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e5e5ea;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.asset-card.dark .loading-spinner {
  border-color: #48484a;
  border-top-color: #0a84ff;
}

.loading-text {
  font-size: 28rpx;
  color: #8e8e93;
}

.asset-card.dark .loading-text {
  color: #98989d;
}

.asset-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.error-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.error-message {
  font-size: 28rpx;
  color: #8e8e93;
  text-align: center;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.asset-card.dark .error-message {
  color: #98989d;
}

.retry-btn {
  background: #007aff;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.asset-card.dark .retry-btn {
  background: #0a84ff;
}

.asset-footer {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f2f2f7;
}

.asset-card.dark .asset-footer {
  border-top-color: #48484a;
}

.view-detail-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  transition: all 0.2s ease;
}

.view-detail-btn:active {
  opacity: 0.6;
}

.detail-text {
  font-size: 28rpx;
  color: #007aff;
  font-weight: 500;
}

.asset-card.dark .detail-text {
  color: #0a84ff;
}

.detail-arrow {
  font-size: 24rpx;
  color: #007aff;
  font-weight: bold;
}

.asset-card.dark .detail-arrow {
  color: #0a84ff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}