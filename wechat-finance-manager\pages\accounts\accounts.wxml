<!--pages/accounts/accounts.wxml-->
<view class="container">
  <!-- 添加账户按钮 -->
  <view class="add-button" bindtap="addAccount">
    <text class="add-icon">+</text>
    <text class="add-text">添加账户</text>
  </view>

  <!-- 刷新提示 -->
  <view class="refresh-hint" wx:if="{{refreshing}}">
    <text class="refresh-text">刷新中...</text>
  </view>

  <!-- 账户列表 -->
  <view class="account-list" wx:if="{{!loading && accounts.length > 0}}">
    <account-card 
      wx:for="{{accounts}}" 
      wx:key="_id" 
      account="{{item}}"
      bind:cardtap="onAccountCardTap"
      bind:edit="onEditAccount"
      bind:delete="onDeleteAccount">
    </account-card>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && accounts.length === 0}}">
    <view class="empty-icon">🏦</view>
    <view class="empty-text">暂无账户</view>
    <view class="empty-desc">点击上方按钮添加您的第一个账户</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>