// cloudfunctions/calculateAssets/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 配置
const CONFIG = {
  cacheExpiry: 2 * 60 * 1000, // 2分钟缓存
  maxRetries: 3,
  retryDelay: 1000
}

// 日志记录
function log(level, message, data = {}) {
  const logData = {
    level,
    message,
    data: JSON.stringify(data),
    timestamp: new Date(),
    source: 'calculateAssets'
  }
  
  console.log(`[${level.toUpperCase()}] ${message}`, data)
  
  db.collection('logs').add({ data: logData }).catch(err => {
    console.error('保存日志失败:', err)
  })
}

// 延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 重试执行
async function executeWithRetry(operation, operationName, maxRetries = CONFIG.maxRetries) {
  let lastError
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      log('info', `执行操作 ${operationName}，第 ${attempt} 次尝试`)
      const result = await operation()
      
      if (attempt > 1) {
        log('info', `操作 ${operationName} 在第 ${attempt} 次尝试后成功`)
      }
      
      return result
    } catch (error) {
      lastError = error
      log('warn', `操作 ${operationName} 第 ${attempt} 次尝试失败`, { error: error.message })
      
      if (attempt < maxRetries) {
        await delay(CONFIG.retryDelay * attempt)
      }
    }
  }
  
  throw lastError
}

// 计算单个持仓的市值
function calculatePositionValue(position, currentPrice) {
  try {
    if (!position || !position.quantity || !currentPrice) {
      throw new Error('持仓数据或价格数据无效')
    }

    const quantity = parseFloat(position.quantity)
    const costPrice = parseFloat(position.costPrice) || 0
    const price = parseFloat(currentPrice.price) || 0

    const marketValue = quantity * price
    const costValue = quantity * costPrice
    const unrealizedPnL = marketValue - costValue
    const unrealizedPnLPercent = costValue > 0 ? (unrealizedPnL / costValue) * 100 : 0

    return {
      symbol: position.symbol,
      name: currentPrice.name || position.name || position.symbol,
      quantity,
      costPrice,
      currentPrice: price,
      marketValue: parseFloat(marketValue.toFixed(2)),
      costValue: parseFloat(costValue.toFixed(2)),
      unrealizedPnL: parseFloat(unrealizedPnL.toFixed(2)),
      unrealizedPnLPercent: parseFloat(unrealizedPnLPercent.toFixed(2)),
      dailyChange: currentPrice.change || 0,
      dailyChangePercent: currentPrice.changePercent || 0,
      dailyPnL: parseFloat((quantity * (currentPrice.change || 0)).toFixed(2))
    }
  } catch (error) {
    log('error', '计算持仓市值失败', { position, currentPrice, error: error.message })
    throw error
  }
}

// 计算账户总资产
async function calculateAccountAssets(accountId) {
  try {
    log('info', '开始计算账户资产', { accountId })

    // 获取账户持仓
    const positionsResult = await db.collection('positions')
      .where({
        accountId: accountId
      })
      .get()

    if (positionsResult.data.length === 0) {
      log('info', '账户无持仓数据', { accountId })
      return {
        accountId,
        totalMarketValue: 0,
        totalCostValue: 0,
        totalUnrealizedPnL: 0,
        totalUnrealizedPnLPercent: 0,
        totalDailyPnL: 0,
        totalDailyPnLPercent: 0,
        positions: [],
        lastUpdated: new Date()
      }
    }

    const positions = positionsResult.data
    const symbols = positions.map(p => p.symbol)

    // 获取当前价格
    const pricesResult = await executeWithRetry(async () => {
      const result = await cloud.callFunction({
        name: 'getPrices',
        data: {
          symbols: symbols
        }
      })

      if (!result.result.success) {
        throw new Error(`获取价格失败: ${result.result.error}`)
      }

      return result.result.data
    }, 'getPrices')

    const prices = pricesResult
    const positionValues = []
    let totalMarketValue = 0
    let totalCostValue = 0
    let totalDailyPnL = 0

    // 计算每个持仓的价值
    for (const position of positions) {
      const currentPrice = prices[position.symbol]
      
      if (!currentPrice) {
        log('warn', '未获取到价格数据', { symbol: position.symbol })
        continue
      }

      const positionValue = calculatePositionValue(position, currentPrice)
      positionValues.push(positionValue)

      totalMarketValue += positionValue.marketValue
      totalCostValue += positionValue.costValue
      totalDailyPnL += positionValue.dailyPnL
    }

    const totalUnrealizedPnL = totalMarketValue - totalCostValue
    const totalUnrealizedPnLPercent = totalCostValue > 0 ? (totalUnrealizedPnL / totalCostValue) * 100 : 0
    const totalDailyPnLPercent = totalMarketValue > 0 ? (totalDailyPnL / (totalMarketValue - totalDailyPnL)) * 100 : 0

    const result = {
      accountId,
      totalMarketValue: parseFloat(totalMarketValue.toFixed(2)),
      totalCostValue: parseFloat(totalCostValue.toFixed(2)),
      totalUnrealizedPnL: parseFloat(totalUnrealizedPnL.toFixed(2)),
      totalUnrealizedPnLPercent: parseFloat(totalUnrealizedPnLPercent.toFixed(2)),
      totalDailyPnL: parseFloat(totalDailyPnL.toFixed(2)),
      totalDailyPnLPercent: parseFloat(totalDailyPnLPercent.toFixed(2)),
      positions: positionValues,
      lastUpdated: new Date()
    }

    log('info', '账户资产计算完成', {
      accountId,
      totalMarketValue: result.totalMarketValue,
      positionCount: positionValues.length
    })

    return result
  } catch (error) {
    log('error', '计算账户资产失败', { accountId, error: error.message })
    throw error
  }
}

// 计算用户总资产
async function calculateUserTotalAssets(userId) {
  try {
    log('info', '开始计算用户总资产', { userId })

    // 获取用户所有账户
    const accountsResult = await db.collection('accounts')
      .where({
        userId: userId,
        isActive: true
      })
      .get()

    if (accountsResult.data.length === 0) {
      log('info', '用户无活跃账户', { userId })
      return {
        userId,
        totalMarketValue: 0,
        totalCostValue: 0,
        totalUnrealizedPnL: 0,
        totalUnrealizedPnLPercent: 0,
        totalDailyPnL: 0,
        totalDailyPnLPercent: 0,
        accounts: [],
        lastUpdated: new Date()
      }
    }

    const accounts = accountsResult.data
    const accountAssets = []
    let totalMarketValue = 0
    let totalCostValue = 0
    let totalDailyPnL = 0

    // 计算每个账户的资产
    for (const account of accounts) {
      try {
        const accountAsset = await calculateAccountAssets(account._id)
        
        const accountAssetWithInfo = {
          ...accountAsset,
          accountInfo: {
            _id: account._id,
            brokerName: account.brokerName,
            accountNumber: account.accountNumber,
            accountType: account.accountType
          }
        }
        
        accountAssets.push(accountAssetWithInfo)
        totalMarketValue += accountAsset.totalMarketValue
        totalCostValue += accountAsset.totalCostValue
        totalDailyPnL += accountAsset.totalDailyPnL
      } catch (error) {
        log('error', '计算账户资产失败', {
          accountId: account._id,
          error: error.message
        })
      }
    }

    const totalUnrealizedPnL = totalMarketValue - totalCostValue
    const totalUnrealizedPnLPercent = totalCostValue > 0 ? (totalUnrealizedPnL / totalCostValue) * 100 : 0
    const totalDailyPnLPercent = totalMarketValue > 0 ? (totalDailyPnL / (totalMarketValue - totalDailyPnL)) * 100 : 0

    const result = {
      userId,
      totalMarketValue: parseFloat(totalMarketValue.toFixed(2)),
      totalCostValue: parseFloat(totalCostValue.toFixed(2)),
      totalUnrealizedPnL: parseFloat(totalUnrealizedPnL.toFixed(2)),
      totalUnrealizedPnLPercent: parseFloat(totalUnrealizedPnLPercent.toFixed(2)),
      totalDailyPnL: parseFloat(totalDailyPnL.toFixed(2)),
      totalDailyPnLPercent: parseFloat(totalDailyPnLPercent.toFixed(2)),
      accounts: accountAssets,
      lastUpdated: new Date()
    }

    log('info', '用户总资产计算完成', {
      userId,
      totalMarketValue: result.totalMarketValue,
      accountCount: accountAssets.length
    })

    return result
  } catch (error) {
    log('error', '计算用户总资产失败', { userId, error: error.message })
    throw error
  }
}

// 保存资产快照
async function saveAssetSnapshot(type, id, assetData) {
  try {
    await db.collection('asset_snapshots').add({
      data: {
        type, // 'account' 或 'user'
        targetId: id,
        ...assetData,
        createdAt: new Date()
      }
    })
    
    log('info', '资产快照已保存', { type, id })
  } catch (error) {
    log('error', '保存资产快照失败', { type, id, error: error.message })
  }
}

// 获取缓存的资产数据
async function getCachedAssets(type, id) {
  try {
    const cacheExpiry = new Date(Date.now() - CONFIG.cacheExpiry)
    
    const result = await db.collection('asset_snapshots')
      .where({
        type: type,
        targetId: id,
        createdAt: _.gte(cacheExpiry)
      })
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get()
    
    if (result.data.length > 0) {
      log('info', '使用缓存的资产数据', { type, id })
      return result.data[0]
    }
    
    return null
  } catch (error) {
    log('error', '获取缓存资产数据失败', { error: error.message })
    return null
  }
}

// 云函数入口
exports.main = async (event, context) => {
  const { type, id, useCache = true, forceRefresh = false } = event
  
  try {
    log('info', '开始资产计算', { type, id, useCache, forceRefresh })
    
    // 参数验证
    if (!type || !id) {
      throw new Error('类型和ID参数不能为空')
    }
    
    if (!['account', 'user'].includes(type)) {
      throw new Error('类型参数必须是 account 或 user')
    }
    
    let result = null
    
    // 如果使用缓存且不强制刷新，先尝试从缓存获取
    if (useCache && !forceRefresh) {
      const cached = await getCachedAssets(type, id)
      if (cached) {
        return {
          success: true,
          data: cached,
          meta: {
            source: 'cache',
            timestamp: new Date()
          }
        }
      }
    }
    
    // 计算资产
    if (type === 'account') {
      result = await calculateAccountAssets(id)
    } else if (type === 'user') {
      result = await calculateUserTotalAssets(id)
    }
    
    // 保存快照
    await saveAssetSnapshot(type, id, result)
    
    log('info', '资产计算完成', { type, id, totalMarketValue: result.totalMarketValue })
    
    return {
      success: true,
      data: result,
      meta: {
        source: 'calculated',
        timestamp: new Date()
      }
    }
    
  } catch (error) {
    log('error', '资产计算失败', { error: error.message, stack: error.stack })
    
    return {
      success: false,
      error: error.message,
      meta: {
        timestamp: new Date()
      }
    }
  }
}