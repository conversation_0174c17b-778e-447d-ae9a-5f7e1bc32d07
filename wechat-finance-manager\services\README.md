# 核心服务层实现

本目录包含微信小程序理财管家应用的核心服务层实现，包括数据服务、认证服务和价格服务。

## 已实现的服务

### 1. DataService (数据服务)

**功能特性：**
- ✅ 数据库连接和基础CRUD操作
- ✅ 数据加密解密功能（使用AES加密）
- ✅ 完整的错误处理机制
- ✅ 日志记录功能
- ✅ 数据验证机制
- ✅ 批量操作支持

**主要方法：**
- `createAccount()` - 创建账户
- `getAccounts()` - 获取账户列表
- `updateAccount()` - 更新账户信息
- `deleteAccount()` - 删除账户
- `addPosition()` - 添加持仓
- `getPositions()` - 获取持仓列表
- `updatePosition()` - 更新持仓
- `deletePosition()` - 删除持仓
- `addTransaction()` - 添加交易记录
- `getTransactions()` - 获取交易记录
- `calculatePnL()` - 计算盈亏

### 2. AuthService (认证服务)

**功能特性：**
- ✅ 微信登录和用户身份验证
- ✅ 用户权限检查和数据访问控制
- ✅ 用户信息管理功能
- ✅ 数据加密存储
- ✅ 会话验证
- ✅ 用户设置管理

**主要方法：**
- `login()` - 微信登录
- `getUserInfo()` - 获取用户信息
- `checkPermission()` - 检查权限
- `checkDataAccess()` - 检查数据访问权限
- `logout()` - 注销登录
- `updateUserSettings()` - 更新用户设置
- `validateSession()` - 验证用户会话

### 3. PriceService (价格服务)

**功能特性：**
- ✅ 价格数据获取接口
- ✅ 价格数据缓存机制（内存+数据库）
- ✅ 价格更新的错误处理逻辑
- ✅ 重试机制
- ✅ 批量更新支持
- ✅ 历史价格数据管理

**主要方法：**
- `getCurrentPrices()` - 获取当前价格
- `getHistoricalPrices()` - 获取历史价格
- `getFundData()` - 获取基金数据
- `updatePriceCache()` - 更新价格缓存
- `warmupCache()` - 预热缓存
- `checkHealth()` - 健康检查

## 安全特性

### 数据加密
- 使用AES加密算法保护敏感数据
- 账户号码等敏感信息加密存储
- 用户信息本地存储加密

### 权限控制
- 基于角色的权限管理
- 数据访问权限检查
- 用户只能访问自己的数据

### 错误处理
- 统一的错误处理机制
- 详细的错误日志记录
- 优雅的错误降级

## 性能优化

### 缓存机制
- 内存缓存：5分钟过期时间
- 数据库缓存：持久化存储
- 批量操作：减少数据库请求

### 重试机制
- 最大重试3次
- 递增延迟策略
- 网络错误自动重试

## 使用示例

```javascript
// 导入服务
import DataService from './services/DataService.js'
import AuthService from './services/AuthService.js'
import PriceService from './services/PriceService.js'

// 使用数据服务
const result = await DataService.createAccount({
  brokerName: '华泰证券',
  accountNumber: '**********'
})

// 使用认证服务
const loginResult = await AuthService.login()
if (loginResult.success) {
  console.log('登录成功', loginResult.data)
}

// 使用价格服务
const prices = await PriceService.getCurrentPrices(['000001', '000002'])
if (prices.success) {
  console.log('价格数据', prices.data)
}
```

## 依赖项

- `crypto-js`: 用于数据加密解密
- 微信云开发SDK

## 测试

运行测试：
```bash
# 在微信开发者工具中运行
node tests/services.test.js
```

## 注意事项

1. 所有服务都使用单例模式，确保全局唯一实例
2. 敏感数据（如账户号码）会自动加密存储
3. 所有操作都有完整的日志记录
4. 网络请求具有重试机制，提高稳定性
5. 缓存机制可以显著提高性能，减少API调用

## 下一步

- 实现数据模型和云数据库设计（任务3）
- 创建主页面和导航实现（任务4）
- 实现证券账户管理功能（任务5）