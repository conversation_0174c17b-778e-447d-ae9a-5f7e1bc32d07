<!--components/theme-switcher/theme-switcher.wxml-->
<view class="theme-switcher {{theme}}">
  <view class="switcher-container">
    <view class="theme-option" 
          wx:for="{{themes}}" 
          wx:key="key"
          bindtap="switchTheme"
          data-theme="{{item.key}}"
          class="{{currentTheme === item.key ? 'active' : ''}}">
      <view class="theme-icon">{{item.icon}}</view>
      <view class="theme-name">{{item.name}}</view>
      <view class="theme-indicator" wx:if="{{currentTheme === item.key}}">
        <text class="indicator-dot"></text>
      </view>
    </view>
  </view>
  
  <!-- 快速切换按钮 -->
  <view class="quick-switch" bindtap="quickSwitch" wx:if="{{showQuickSwitch}}">
    <view class="switch-icon">{{currentTheme === 'light' ? '🌙' : '☀️'}}</view>
  </view>
</view>