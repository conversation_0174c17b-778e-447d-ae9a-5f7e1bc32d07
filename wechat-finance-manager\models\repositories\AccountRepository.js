/**
 * 账户数据仓库
 * 负责账户数据的数据库操作
 */

const Account = require('../Account');
const { COLLECTION_NAME } = require('../collections/accounts');

class AccountRepository {
  constructor(db) {
    this.db = db;
    this.collection = db.collection(COLLECTION_NAME);
  }

  /**
   * 创建新账户
   * @param {Account} account 账户实例
   * @returns {Promise<Object>} 创建结果
   */
  async create(account) {
    try {
      // 验证账户数据
      const validation = account.validate();
      if (!validation.isValid) {
        throw new Error(`账户数据验证失败: ${validation.errors.join(', ')}`);
      }

      // 检查账户是否已存在
      const existingAccount = await this.findByAccountNumber(
        account.accountNumber, 
        account.brokerName
      );
      if (existingAccount) {
        throw new Error('该券商的账户号码已存在');
      }

      // 保存到数据库
      const result = await this.collection.add({
        data: account.toDatabase()
      });

      return {
        success: true,
        _id: result._id,
        message: '账户创建成功'
      };
    } catch (error) {
      console.error('创建账户失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 根据账户号码和券商查找账户
   * @param {string} accountNumber 账户号码
   * @param {string} brokerName 券商名称
   * @returns {Promise<Account|null>} 账户实例或null
   */
  async findByAccountNumber(accountNumber, brokerName) {
    try {
      const result = await this.collection.where({
        accountNumber: accountNumber,
        brokerName: brokerName
      }).get();

      if (result.data.length === 0) {
        return null;
      }

      return Account.fromDatabase(result.data[0]);
    } catch (error) {
      console.error('查找账户失败:', error);
      return null;
    }
  }

  /**
   * 根据ID查找账户
   * @param {string} accountId 账户ID
   * @returns {Promise<Account|null>} 账户实例或null
   */
  async findById(accountId) {
    try {
      const result = await this.collection.doc(accountId).get();

      if (!result.data) {
        return null;
      }

      return Account.fromDatabase(result.data);
    } catch (error) {
      console.error('查找账户失败:', error);
      return null;
    }
  }

  /**
   * 根据用户ID查找所有账户
   * @param {string} userId 用户ID
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 账户列表
   */
  async findByUserId(userId, options = {}) {
    try {
      const { 
        accountType = null, 
        isActive = null, 
        limit = 50, 
        skip = 0,
        orderBy = 'createdAt',
        order = 'desc'
      } = options;

      let query = this.collection.where({
        userId: userId
      });

      // 添加账户类型筛选
      if (accountType) {
        query = query.where({
          accountType: accountType
        });
      }

      // 添加激活状态筛选
      if (isActive !== null) {
        query = query.where({
          isActive: isActive
        });
      }

      // 添加排序
      if (order === 'desc') {
        query = query.orderBy(orderBy, 'desc');
      } else {
        query = query.orderBy(orderBy, 'asc');
      }

      // 添加分页
      query = query.skip(skip).limit(limit);

      const result = await query.get();

      return result.data.map(accountData => Account.fromDatabase(accountData));
    } catch (error) {
      console.error('查找用户账户失败:', error);
      return [];
    }
  }

  /**
   * 更新账户信息
   * @param {string} accountId 账户ID
   * @param {Object} updateData 更新数据
   * @param {string} userId 用户ID（用于权限验证）
   * @returns {Promise<Object>} 更新结果
   */
  async update(accountId, updateData, userId) {
    try {
      // 先获取现有账户数据
      const existingAccount = await this.findById(accountId);
      if (!existingAccount) {
        throw new Error('账户不存在');
      }

      // 检查权限
      if (!existingAccount.checkPermission(userId)) {
        throw new Error('无权限操作此账户');
      }

      // 如果更新账户号码或券商，检查是否重复
      if (updateData.accountNumber || updateData.brokerName) {
        const newAccountNumber = updateData.accountNumber || existingAccount.accountNumber;
        const newBrokerName = updateData.brokerName || existingAccount.brokerName;
        
        const duplicateAccount = await this.findByAccountNumber(newAccountNumber, newBrokerName);
        if (duplicateAccount && duplicateAccount._id !== accountId) {
          throw new Error('该券商的账户号码已存在');
        }
      }

      // 更新账户数据
      existingAccount.update(updateData);

      // 验证更新后的数据
      const validation = existingAccount.validate();
      if (!validation.isValid) {
        throw new Error(`账户数据验证失败: ${validation.errors.join(', ')}`);
      }

      // 保存到数据库
      await this.collection.doc(accountId).update({
        data: existingAccount.toDatabase()
      });

      return {
        success: true,
        message: '账户信息更新成功'
      };
    } catch (error) {
      console.error('更新账户失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 删除账户
   * @param {string} accountId 账户ID
   * @param {string} userId 用户ID（用于权限验证）
   * @returns {Promise<Object>} 删除结果
   */
  async delete(accountId, userId) {
    try {
      // 检查账户是否存在
      const existingAccount = await this.findById(accountId);
      if (!existingAccount) {
        throw new Error('账户不存在');
      }

      // 检查权限
      if (!existingAccount.checkPermission(userId)) {
        throw new Error('无权限操作此账户');
      }

      // 删除账户数据
      await this.collection.doc(accountId).remove();

      return {
        success: true,
        message: '账户删除成功'
      };
    } catch (error) {
      console.error('删除账户失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 统计用户账户数量
   * @param {string} userId 用户ID
   * @param {Object} filters 筛选条件
   * @returns {Promise<number>} 账户数量
   */
  async countByUserId(userId, filters = {}) {
    try {
      let query = this.collection.where({
        userId: userId
      });

      // 添加筛选条件
      if (filters.accountType) {
        query = query.where({
          accountType: filters.accountType
        });
      }

      if (filters.isActive !== undefined) {
        query = query.where({
          isActive: filters.isActive
        });
      }

      const result = await query.count();
      return result.total;
    } catch (error) {
      console.error('统计账户数量失败:', error);
      return 0;
    }
  }

  /**
   * 更新账户资产价值
   * @param {string} accountId 账户ID
   * @param {number} totalValue 总价值
   * @param {number} dailyChange 日变化
   * @param {number} dailyChangePercent 日变化百分比
   * @returns {Promise<Object>} 更新结果
   */
  async updateAssetValue(accountId, totalValue, dailyChange, dailyChangePercent) {
    try {
      await this.collection.doc(accountId).update({
        data: {
          totalValue: totalValue,
          dailyChange: dailyChange,
          dailyChangePercent: dailyChangePercent,
          updatedAt: new Date()
        }
      });

      return {
        success: true,
        message: '账户资产价值更新成功'
      };
    } catch (error) {
      console.error('更新账户资产价值失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 批量更新账户资产价值
   * @param {Array} updates 更新数据数组 [{accountId, totalValue, dailyChange, dailyChangePercent}]
   * @returns {Promise<Object>} 更新结果
   */
  async batchUpdateAssetValues(updates) {
    try {
      const promises = updates.map(update => 
        this.updateAssetValue(
          update.accountId, 
          update.totalValue, 
          update.dailyChange, 
          update.dailyChangePercent
        )
      );

      await Promise.all(promises);

      return {
        success: true,
        message: `批量更新${updates.length}个账户的资产价值成功`
      };
    } catch (error) {
      console.error('批量更新账户资产价值失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取用户资产统计
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 资产统计信息
   */
  async getUserAssetSummary(userId) {
    try {
      const accounts = await this.findByUserId(userId, { isActive: true });
      
      const summary = {
        totalAccounts: accounts.length,
        securitiesAccounts: 0,
        fundAccounts: 0,
        totalValue: 0,
        totalDailyChange: 0,
        totalDailyChangePercent: 0
      };

      accounts.forEach(account => {
        if (account.accountType === 'securities') {
          summary.securitiesAccounts++;
        } else if (account.accountType === 'fund') {
          summary.fundAccounts++;
        }

        summary.totalValue += account.totalValue;
        summary.totalDailyChange += account.dailyChange;
      });

      // 计算总的日变化百分比
      if (summary.totalValue > 0) {
        const previousValue = summary.totalValue - summary.totalDailyChange;
        if (previousValue > 0) {
          summary.totalDailyChangePercent = (summary.totalDailyChange / previousValue) * 100;
        }
      }

      return summary;
    } catch (error) {
      console.error('获取用户资产统计失败:', error);
      return {
        totalAccounts: 0,
        securitiesAccounts: 0,
        fundAccounts: 0,
        totalValue: 0,
        totalDailyChange: 0,
        totalDailyChangePercent: 0
      };
    }
  }
}

module.exports = AccountRepository;