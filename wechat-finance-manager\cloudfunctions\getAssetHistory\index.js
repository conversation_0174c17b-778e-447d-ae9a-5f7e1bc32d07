// cloudfunctions/getAssetHistory/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取资产历史数据
 * @param {Object} event 
 * @param {string} event.userId 用户ID
 * @param {string} event.timeRange 时间范围
 * @param {Array} event.dateRange 日期范围
 */
exports.main = async (event, context) => {
  const { userId, timeRange, dateRange } = event
  
  try {
    // 验证参数
    if (!userId) {
      return {
        success: false,
        error: '用户ID不能为空'
      }
    }

    // 计算时间范围
    const endDate = new Date()
    let startDate = new Date()
    
    switch (timeRange) {
      case 'month':
        startDate.setDate(endDate.getDate() - 30)
        break
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3)
        break
      case 'halfYear':
        startDate.setMonth(endDate.getMonth() - 6)
        break
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
      case 'all':
        startDate.setFullYear(endDate.getFullYear() - 10) // 最多查询10年
        break
      default:
        startDate.setDate(endDate.getDate() - 30)
    }

    // 查询资产历史记录
    const assetHistoryResult = await db.collection('asset_history')
      .where({
        userId: userId,
        date: db.command.gte(startDate).and(db.command.lte(endDate))
      })
      .orderBy('date', 'asc')
      .get()

    let historyData = assetHistoryResult.data

    // 如果没有历史数据，尝试生成当前数据
    if (historyData.length === 0) {
      const currentAssets = await calculateCurrentAssets(userId)
      if (currentAssets > 0) {
        // 创建当前时间点的记录
        const currentRecord = {
          userId: userId,
          date: new Date(),
          totalValue: currentAssets,
          createdAt: new Date()
        }
        
        // 保存到数据库
        await db.collection('asset_history').add({
          data: currentRecord
        })
        
        historyData = [currentRecord]
      }
    }

    // 如果仍然没有数据，返回空数组
    if (historyData.length === 0) {
      return {
        success: true,
        data: []
      }
    }

    // 根据时间范围聚合数据
    const aggregatedData = aggregateDataByTimeRange(historyData, timeRange)

    return {
      success: true,
      data: aggregatedData
    }

  } catch (error) {
    console.error('获取资产历史数据失败:', error)
    return {
      success: false,
      error: error.message || '获取数据失败'
    }
  }
}

/**
 * 计算当前资产总值
 * @param {string} userId 用户ID
 * @returns {number} 资产总值
 */
async function calculateCurrentAssets(userId) {
  try {
    // 获取用户所有账户
    const accountsResult = await db.collection('accounts')
      .where({
        userId: userId,
        isActive: true
      })
      .get()

    if (accountsResult.data.length === 0) {
      return 0
    }

    let totalAssets = 0

    // 计算每个账户的资产
    for (const account of accountsResult.data) {
      // 获取账户持仓
      const positionsResult = await db.collection('positions')
        .where({
          accountId: account._id
        })
        .get()

      // 计算持仓价值
      for (const position of positionsResult.data) {
        const marketValue = position.quantity * (position.currentPrice || position.costPrice)
        totalAssets += marketValue
      }
    }

    return totalAssets

  } catch (error) {
    console.error('计算当前资产失败:', error)
    return 0
  }
}

/**
 * 根据时间范围聚合数据
 * @param {Array} historyData 历史数据
 * @param {string} timeRange 时间范围
 * @returns {Array} 聚合后的数据
 */
function aggregateDataByTimeRange(historyData, timeRange) {
  if (timeRange === 'month') {
    // 按天聚合
    return aggregateByDay(historyData)
  } else if (timeRange === 'quarter') {
    // 按周聚合
    return aggregateByWeek(historyData)
  } else if (timeRange === 'halfYear' || timeRange === 'year') {
    // 按月聚合
    return aggregateByMonth(historyData)
  } else {
    // 按年聚合
    return aggregateByYear(historyData)
  }
}

/**
 * 按天聚合数据
 */
function aggregateByDay(data) {
  const dayMap = new Map()
  
  data.forEach(item => {
    const date = new Date(item.date)
    const dayKey = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`
    
    if (!dayMap.has(dayKey)) {
      dayMap.set(dayKey, {
        date: date.getTime(),
        totalValue: item.totalValue,
        count: 1
      })
    } else {
      const existing = dayMap.get(dayKey)
      existing.totalValue = (existing.totalValue * existing.count + item.totalValue) / (existing.count + 1)
      existing.count++
    }
  })
  
  return Array.from(dayMap.values()).sort((a, b) => a.date - b.date)
}

/**
 * 按周聚合数据
 */
function aggregateByWeek(data) {
  const weekMap = new Map()
  
  data.forEach(item => {
    const date = new Date(item.date)
    const weekStart = new Date(date)
    weekStart.setDate(date.getDate() - date.getDay()) // 周一作为开始
    const weekKey = weekStart.getTime()
    
    if (!weekMap.has(weekKey)) {
      weekMap.set(weekKey, {
        date: weekKey,
        totalValue: item.totalValue,
        count: 1
      })
    } else {
      const existing = weekMap.get(weekKey)
      existing.totalValue = (existing.totalValue * existing.count + item.totalValue) / (existing.count + 1)
      existing.count++
    }
  })
  
  return Array.from(weekMap.values()).sort((a, b) => a.date - b.date)
}

/**
 * 按月聚合数据
 */
function aggregateByMonth(data) {
  const monthMap = new Map()
  
  data.forEach(item => {
    const date = new Date(item.date)
    const monthKey = `${date.getFullYear()}-${date.getMonth()}`
    
    if (!monthMap.has(monthKey)) {
      monthMap.set(monthKey, {
        date: new Date(date.getFullYear(), date.getMonth(), 1).getTime(),
        totalValue: item.totalValue,
        count: 1
      })
    } else {
      const existing = monthMap.get(monthKey)
      existing.totalValue = (existing.totalValue * existing.count + item.totalValue) / (existing.count + 1)
      existing.count++
    }
  })
  
  return Array.from(monthMap.values()).sort((a, b) => a.date - b.date)
}

/**
 * 按年聚合数据
 */
function aggregateByYear(data) {
  const yearMap = new Map()
  
  data.forEach(item => {
    const date = new Date(item.date)
    const yearKey = date.getFullYear()
    
    if (!yearMap.has(yearKey)) {
      yearMap.set(yearKey, {
        date: new Date(yearKey, 0, 1).getTime(),
        totalValue: item.totalValue,
        count: 1
      })
    } else {
      const existing = yearMap.get(yearKey)
      existing.totalValue = (existing.totalValue * existing.count + item.totalValue) / (existing.count + 1)
      existing.count++
    }
  })
  
  return Array.from(yearMap.values()).sort((a, b) => a.date - b.date)
}