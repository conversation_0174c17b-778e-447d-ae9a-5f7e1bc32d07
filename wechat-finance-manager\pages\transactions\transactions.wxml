<!--pages/transactions/transactions.wxml-->
<view class="container">
  <!-- 添加交易按钮 -->
  <view class="add-button" bindtap="addTransaction">
    <text class="add-icon">+</text>
    <text class="add-text">添加交易记录</text>
  </view>

  <!-- 交易记录列表 -->
  <view class="transaction-list" wx:if="{{!loading && transactions.length > 0}}">
    <view class="transaction-item" wx:for="{{transactions}}" wx:key="id">
      <!-- 交易记录内容 -->
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && transactions.length === 0}}">
    <view class="empty-icon">📈</view>
    <view class="empty-text">暂无交易记录</view>
    <view class="empty-desc">点击上方按钮添加您的第一笔交易</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>