// pages/account-detail/account-detail.js
import dataService from '../../services/DataService.js'

Page({
  data: {
    accountId: '',
    account: null,
    positions: [],
    filteredPositions: [],
    loading: true,
    refreshing: false,
    statistics: {
      totalValue: 0,
      totalCost: 0,
      totalPnL: 0,
      totalPnLPercent: 0,
      positionCount: 0
    },
    
    // 筛选和排序
    filterOptions: {
      type: 'all', // all, stock, fund, bond
      status: 'all', // all, profit, loss, break-even
      sortBy: 'marketValue', // marketValue, unrealizedPnL, symbol, quantity
      sortOrder: 'desc' // desc, asc
    },
    
    // 批量操作
    batchMode: false,
    selectedPositions: [],
    
    // 筛选选项
    typeOptions: [
      { value: 'all', label: '全部类型' },
      { value: 'stock', label: '股票' },
      { value: 'fund', label: '基金' },
      { value: 'bond', label: '债券' }
    ],
    
    statusOptions: [
      { value: 'all', label: '全部状态' },
      { value: 'profit', label: '盈利' },
      { value: 'loss', label: '亏损' },
      { value: 'break-even', label: '持平' }
    ],
    
    sortOptions: [
      { value: 'marketValue', label: '市值' },
      { value: 'unrealizedPnL', label: '盈亏' },
      { value: 'symbol', label: '代码' },
      { value: 'quantity', label: '数量' }
    ]
  },

  onLoad(options) {
    const { id } = options
    if (!id) {
      wx.showToast({
        title: '账户ID不能为空',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({
      accountId: id
    })
    this.loadAccountDetail()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.accountId) {
      this.loadAccountDetail()
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshAccountDetail()
  },

  async loadAccountDetail() {
    this.setData({
      loading: true
    })

    try {
      // 并行加载账户信息和持仓数据
      const [accountResult, positionsResult] = await Promise.all([
        this.loadAccountInfo(),
        this.loadPositions()
      ])

      if (accountResult && positionsResult) {
        this.calculateStatistics()
      }
    } catch (error) {
      console.error('加载账户详情失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }

    this.setData({
      loading: false
    })
  },

  // 加载账户基本信息
  async loadAccountInfo() {
    try {
      const result = await dataService.getAccounts()
      if (result.success) {
        const account = result.data.find(acc => acc._id === this.data.accountId)
        if (account) {
          this.setData({
            account: account
          })
          // 更新页面标题
          wx.setNavigationBarTitle({
            title: account.brokerName
          })
          return true
        } else {
          wx.showToast({
            title: '账户不存在',
            icon: 'error'
          })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
          return false
        }
      }
    } catch (error) {
      console.error('加载账户信息失败:', error)
      return false
    }
  },

  // 加载持仓数据
  async loadPositions() {
    try {
      const result = await dataService.getPositions(this.data.accountId)
      if (result.success) {
        this.setData({
          positions: result.data || []
        })
        // 应用筛选和排序
        this.applyFilterAndSort()
        return true
      } else {
        console.error('加载持仓数据失败:', result.error)
        return false
      }
    } catch (error) {
      console.error('加载持仓数据异常:', error)
      return false
    }
  },

  // 刷新账户详情
  async refreshAccountDetail() {
    this.setData({
      refreshing: true
    })

    try {
      await this.loadAccountDetail()
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      })
    }

    this.setData({
      refreshing: false
    })
    wx.stopPullDownRefresh()
  },

  // 计算统计数据
  calculateStatistics() {
    const positions = this.data.positions
    let totalValue = 0
    let totalCost = 0

    positions.forEach(position => {
      const marketValue = position.marketValue || (position.quantity * position.currentPrice)
      const costValue = position.quantity * position.costPrice
      
      totalValue += marketValue
      totalCost += costValue
    })

    const totalPnL = totalValue - totalCost
    const totalPnLPercent = totalCost > 0 ? (totalPnL / totalCost) * 100 : 0

    this.setData({
      statistics: {
        totalValue: totalValue,
        totalCost: totalCost,
        totalPnL: totalPnL,
        totalPnLPercent: totalPnLPercent,
        positionCount: positions.length
      }
    })
  },

  // 添加持仓
  addPosition() {
    wx.navigateTo({
      url: `/pages/add-position/add-position?accountId=${this.data.accountId}`
    })
  },

  // 导入持仓
  importPositions() {
    wx.navigateTo({
      url: `/pages/import-positions/import-positions?accountId=${this.data.accountId}`
    })
  },

  // 查看持仓详情
  viewPositionDetail(e) {
    const positionId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/position-detail/position-detail?id=${positionId}`
    })
  },

  // 编辑持仓
  editPosition(e) {
    const position = e.currentTarget.dataset.position
    wx.navigateTo({
      url: `/pages/add-position/add-position?mode=edit&id=${position._id}&accountId=${this.data.accountId}`
    })
  },

  // 删除持仓
  async deletePosition(e) {
    const positionId = e.currentTarget.dataset.id
    const positionName = e.currentTarget.dataset.name

    wx.showModal({
      title: '确认删除',
      content: `确定要删除持仓"${positionName}"吗？`,
      confirmText: '删除',
      confirmColor: '#FF3B30',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...'
          })

          try {
            const result = await dataService.deletePosition(positionId)
            wx.hideLoading()

            if (result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })
              // 重新加载持仓数据
              this.loadPositions().then(() => {
                this.calculateStatistics()
              })
            } else {
              wx.showToast({
                title: result.error?.message || '删除失败',
                icon: 'error'
              })
            }
          } catch (error) {
            wx.hideLoading()
            console.error('删除持仓失败:', error)
            wx.showToast({
              title: '删除异常',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  // 格式化数值显示
  formatValue(value) {
    if (!value) return '0.00'
    return parseFloat(value).toFixed(2)
  },

  // 格式化百分比显示
  formatPercent(value) {
    if (!value) return '0.00'
    return parseFloat(value).toFixed(2)
  },

  // 获取盈亏颜色类名
  getPnLColorClass(value) {
    if (value > 0) return 'positive'
    if (value < 0) return 'negative'
    return 'neutral'
  },

  // 应用筛选和排序
  applyFilterAndSort() {
    const { positions, filterOptions } = this.data
    let filteredPositions = [...positions]

    // 按类型筛选
    if (filterOptions.type !== 'all') {
      filteredPositions = filteredPositions.filter(position => 
        position.type === filterOptions.type
      )
    }

    // 按盈亏状态筛选
    if (filterOptions.status !== 'all') {
      filteredPositions = filteredPositions.filter(position => {
        const pnl = position.unrealizedPnL || (position.quantity * position.currentPrice - position.quantity * position.costPrice)
        if (filterOptions.status === 'profit') return pnl > 0
        if (filterOptions.status === 'loss') return pnl < 0
        if (filterOptions.status === 'break-even') return pnl === 0
        return true
      })
    }

    // 排序
    filteredPositions.sort((a, b) => {
      let aValue, bValue
      
      switch (filterOptions.sortBy) {
        case 'marketValue':
          aValue = a.marketValue || (a.quantity * a.currentPrice)
          bValue = b.marketValue || (b.quantity * b.currentPrice)
          break
        case 'unrealizedPnL':
          aValue = a.unrealizedPnL || (a.quantity * a.currentPrice - a.quantity * a.costPrice)
          bValue = b.unrealizedPnL || (b.quantity * b.currentPrice - b.quantity * b.costPrice)
          break
        case 'symbol':
          aValue = a.symbol
          bValue = b.symbol
          break
        case 'quantity':
          aValue = a.quantity
          bValue = b.quantity
          break
        default:
          aValue = a.marketValue || (a.quantity * a.currentPrice)
          bValue = b.marketValue || (b.quantity * b.currentPrice)
      }

      if (typeof aValue === 'string') {
        return filterOptions.sortOrder === 'desc' 
          ? bValue.localeCompare(aValue)
          : aValue.localeCompare(bValue)
      } else {
        return filterOptions.sortOrder === 'desc' 
          ? bValue - aValue 
          : aValue - bValue
      }
    })

    this.setData({
      filteredPositions
    })
  },

  // 筛选类型改变
  onFilterTypeChange(e) {
    const type = e.detail.value
    const typeOption = this.data.typeOptions[type]
    
    this.setData({
      'filterOptions.type': typeOption.value
    })
    this.applyFilterAndSort()
  },

  // 筛选状态改变
  onFilterStatusChange(e) {
    const status = e.detail.value
    const statusOption = this.data.statusOptions[status]
    
    this.setData({
      'filterOptions.status': statusOption.value
    })
    this.applyFilterAndSort()
  },

  // 排序方式改变
  onSortByChange(e) {
    const sortBy = e.detail.value
    const sortOption = this.data.sortOptions[sortBy]
    
    this.setData({
      'filterOptions.sortBy': sortOption.value
    })
    this.applyFilterAndSort()
  },

  // 排序顺序切换
  toggleSortOrder() {
    const newOrder = this.data.filterOptions.sortOrder === 'desc' ? 'asc' : 'desc'
    this.setData({
      'filterOptions.sortOrder': newOrder
    })
    this.applyFilterAndSort()
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterOptions: {
        type: 'all',
        status: 'all',
        sortBy: 'marketValue',
        sortOrder: 'desc'
      }
    })
    this.applyFilterAndSort()
  },

  // 切换批量操作模式
  toggleBatchMode() {
    const newBatchMode = !this.data.batchMode
    this.setData({
      batchMode: newBatchMode,
      selectedPositions: []
    })
    
    if (newBatchMode) {
      wx.showToast({
        title: '已进入批量操作模式',
        icon: 'none'
      })
    }
  },

  // 选择/取消选择持仓
  togglePositionSelection(e) {
    if (!this.data.batchMode) return
    
    const positionId = e.currentTarget.dataset.id
    const selectedPositions = [...this.data.selectedPositions]
    const index = selectedPositions.indexOf(positionId)
    
    if (index > -1) {
      selectedPositions.splice(index, 1)
    } else {
      selectedPositions.push(positionId)
    }
    
    this.setData({
      selectedPositions
    })
  },

  // 全选/取消全选
  toggleSelectAll() {
    const { filteredPositions, selectedPositions } = this.data
    const allSelected = selectedPositions.length === filteredPositions.length
    
    if (allSelected) {
      this.setData({
        selectedPositions: []
      })
    } else {
      this.setData({
        selectedPositions: filteredPositions.map(p => p._id)
      })
    }
  },

  // 批量删除
  batchDelete() {
    const { selectedPositions } = this.data
    
    if (selectedPositions.length === 0) {
      wx.showToast({
        title: '请选择要删除的持仓',
        icon: 'none'
      })
      return
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedPositions.length}个持仓吗？`,
      confirmText: '删除',
      confirmColor: '#FF3B30',
      success: async (res) => {
        if (res.confirm) {
          await this.performBatchDelete()
        }
      }
    })
  },

  // 执行批量删除
  async performBatchDelete() {
    const { selectedPositions } = this.data
    
    wx.showLoading({
      title: '删除中...'
    })
    
    let successCount = 0
    let failCount = 0
    
    for (const positionId of selectedPositions) {
      try {
        const result = await dataService.deletePosition(positionId)
        if (result.success) {
          successCount++
        } else {
          failCount++
        }
      } catch (error) {
        failCount++
        console.error('删除持仓失败:', error)
      }
    }
    
    wx.hideLoading()
    
    // 显示结果
    const message = `删除完成！成功：${successCount}个，失败：${failCount}个`
    wx.showToast({
      title: message,
      icon: successCount > 0 ? 'success' : 'error'
    })
    
    // 退出批量模式并刷新数据
    this.setData({
      batchMode: false,
      selectedPositions: []
    })
    
    this.loadPositions().then(() => {
      this.calculateStatistics()
    })
  },

  // 批量导出
  batchExport() {
    const { selectedPositions, positions } = this.data
    
    if (selectedPositions.length === 0) {
      wx.showToast({
        title: '请选择要导出的持仓',
        icon: 'none'
      })
      return
    }
    
    // 获取选中的持仓数据
    const selectedData = positions.filter(p => selectedPositions.includes(p._id))
    
    // 生成CSV内容
    const headers = ['证券代码', '证券名称', '证券类型', '持仓数量', '成本价', '当前价', '市值', '盈亏', '备注']
    const csvRows = [headers.join(',')]
    
    selectedData.forEach(position => {
      const row = [
        position.symbol,
        position.name || '',
        position.type,
        position.quantity,
        position.costPrice,
        position.currentPrice || position.costPrice,
        position.marketValue || (position.quantity * (position.currentPrice || position.costPrice)),
        position.unrealizedPnL || 0,
        position.notes || ''
      ]
      csvRows.push(row.join(','))
    })
    
    const csvContent = csvRows.join('\n')
    
    // 保存文件
    const fs = wx.getFileSystemManager()
    const fileName = `positions_export_${new Date().getTime()}.csv`
    const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`
    
    fs.writeFile({
      filePath,
      data: csvContent,
      encoding: 'utf8',
      success: () => {
        wx.showModal({
          title: '导出成功',
          content: `已导出${selectedPositions.length}个持仓到文件：${fileName}`,
          showCancel: false
        })
      },
      fail: (error) => {
        console.error('导出失败:', error)
        wx.showToast({
          title: '导出失败',
          icon: 'error'
        })
      }
    })
  },

  // 检查持仓是否被选中
  isPositionSelected(positionId) {
    return this.data.selectedPositions.includes(positionId)
  },

  // 获取筛选选项显示文本
  getFilterTypeText() {
    const option = this.data.typeOptions.find(opt => opt.value === this.data.filterOptions.type)
    return option ? option.label : '全部类型'
  },

  getFilterStatusText() {
    const option = this.data.statusOptions.find(opt => opt.value === this.data.filterOptions.status)
    return option ? option.label : '全部状态'
  },

  getSortByText() {
    const option = this.data.sortOptions.find(opt => opt.value === this.data.filterOptions.sortBy)
    return option ? option.label : '市值'
  }
})