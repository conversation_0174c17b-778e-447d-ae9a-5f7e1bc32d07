<!--pages/asset-chart/asset-chart.wxml-->
<view class="page-container" data-theme="{{theme}}">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">资产变化</view>
    <view class="header-actions">
      <view class="action-btn" bindtap="onExportChart">
        <text class="action-icon">📤</text>
      </view>
      <view class="action-btn" bindtap="onShareChart">
        <text class="action-icon">📤</text>
      </view>
    </view>
  </view>

  <!-- 资产概览 -->
  <view class="asset-overview">
    <view class="overview-main">
      <view class="total-assets">
        <text class="assets-label">总资产</text>
        <text class="assets-value">¥{{totalAssets.toLocaleString()}}</text>
      </view>
      <view class="daily-change {{dailyChange >= 0 ? 'positive' : 'negative'}}">
        <text class="change-value">{{dailyChange >= 0 ? '+' : ''}}¥{{dailyChange.toLocaleString()}}</text>
        <text class="change-percent">({{dailyChange >= 0 ? '+' : ''}}{{(dailyChangePercent * 100).toFixed(2)}}%)</text>
      </view>
    </view>
  </view>

  <!-- 时间范围选择 -->
  <view class="time-range-selector">
    <scroll-view class="range-scroll" scroll-x="true" show-scrollbar="false">
      <view class="range-list">
        <view 
          wx:for="{{timeRanges}}" 
          wx:key="key"
          class="range-item {{item.active ? 'active' : ''}}"
          data-range="{{item.key}}"
          bindtap="onTimeRangeChange"
        >
          <text class="range-text">{{item.label}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 图表容器 -->
  <view class="chart-section">
    <view class="chart-wrapper">
      <chart
        id="asset-chart"
        data="{{chartData}}"
        theme="{{theme}}"
        time-range="{{currentTimeRange}}"
        chart-type="line"
        height="{{chartHeight}}"
        bind:ready="onChartReady"
        bind:update="onChartUpdate"
        bind:chartClick="onChartClick"
        bind:error="onChartError"
      />
    </view>
  </view>

  <!-- 数据说明 -->
  <view class="data-info" wx:if="{{!loading && chartData.length > 0}}">
    <view class="info-item">
      <text class="info-label">数据点数</text>
      <text class="info-value">{{chartData.length}}个</text>
    </view>
    <view class="info-item">
      <text class="info-label">时间范围</text>
      <text class="info-value">{{currentTimeRange === 'month' ? '最近30天' : currentTimeRange === 'quarter' ? '最近3个月' : currentTimeRange === 'halfYear' ? '最近6个月' : currentTimeRange === 'year' ? '最近12个月' : '全部历史'}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">更新时间</text>
      <text class="info-value">刚刚</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && chartData.length === 0}}">
    <view class="empty-icon">📈</view>
    <view class="empty-title">暂无资产数据</view>
    <view class="empty-desc">请先添加证券账户和持仓信息</view>
    <view class="empty-actions">
      <navigator url="/pages/accounts/accounts" class="action-button primary">
        <text>管理账户</text>
      </navigator>
    </view>
  </view>
</view>