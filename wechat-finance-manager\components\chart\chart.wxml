<!--components/chart/chart.wxml-->
<view class="chart-container">
  <!-- ECharts画布 -->
  <ec-canvas 
    id="ec-canvas"
    canvas-id="ec-canvas"
    ec="{{ ec }}"
    echarts="{{ echarts }}"
    class="chart-canvas"
    style="height: {{height}}rpx;"
    bindtouchstart="onChartClick"
    wx:if="{{!loading && !isEmpty}}"
  ></ec-canvas>

  <!-- 加载状态 -->
  <view class="chart-loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">图表加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="chart-empty" wx:if="{{!loading && isEmpty}}">
    <view class="empty-icon">📊</view>
    <text class="empty-text">暂无图表数据</text>
  </view>
</view>