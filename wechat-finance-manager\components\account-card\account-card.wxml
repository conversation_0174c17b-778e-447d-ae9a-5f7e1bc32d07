<!--components/account-card/account-card.wxml-->
<view class="account-card" bindtap="onCardTap">
  <view class="card-content">
    <!-- 账户基本信息 -->
    <view class="account-info">
      <view class="broker-name">{{account.brokerName}}</view>
      <view class="account-number">{{formatAccountNumber(account.accountNumber)}}</view>
    </view>

    <!-- 账户价值信息 -->
    <view class="account-value">
      <view class="total-value">¥{{formatValue(account.totalValue)}}</view>
      <view class="daily-change {{account.dailyChange >= 0 ? 'positive' : 'negative'}}" wx:if="{{account.dailyChange !== undefined}}">
        {{account.dailyChange >= 0 ? '+' : ''}}{{formatValue(account.dailyChange)}}
        <text class="change-percent" wx:if="{{account.dailyChangePercent !== undefined}}">
          ({{account.dailyChangePercent >= 0 ? '+' : ''}}{{formatValue(account.dailyChangePercent)}}%)
        </text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-button" bindtap="showActionMenu">
      <text class="action-icon">⋯</text>
    </view>
  </view>

  <!-- 操作菜单 -->
  <view class="action-menu {{showMenu ? 'show' : ''}}" wx:if="{{showMenu}}" bindtap="hideActionMenu">
    <view class="menu-content" catchtap="">
      <view class="menu-item" bindtap="editAccount">
        <text class="menu-icon">✏️</text>
        <text class="menu-text">编辑</text>
      </view>
      <view class="menu-item delete" bindtap="deleteAccount">
        <text class="menu-icon">🗑️</text>
        <text class="menu-text">删除</text>
      </view>
    </view>
  </view>
</view>