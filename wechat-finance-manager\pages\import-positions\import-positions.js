// pages/import-positions/import-positions.js
import dataService from '../../services/DataService.js'
import Position from '../../models/Position.js'
import { validateSymbol } from '../../models/collections/positions.js'

Page({
  data: {
    accountId: '',
    
    // 导入状态
    importStep: 'select', // select, preview, importing, complete
    
    // 文件信息
    fileInfo: {
      name: '',
      size: 0,
      type: ''
    },
    
    // 解析后的数据
    parsedData: [],
    validData: [],
    invalidData: [],
    
    // 导入进度
    importProgress: {
      total: 0,
      current: 0,
      success: 0,
      failed: 0,
      errors: []
    },
    
    // 页面状态
    loading: false,
    parsing: false,
    importing: false,
    
    // 模板信息
    templateColumns: [
      { key: 'symbol', name: '证券代码', required: true, example: '000001' },
      { key: 'name', name: '证券名称', required: false, example: '平安银行' },
      { key: 'type', name: '证券类型', required: false, example: 'stock' },
      { key: 'quantity', name: '持仓数量', required: true, example: '1000' },
      { key: 'costPrice', name: '成本价', required: true, example: '12.50' },
      { key: 'notes', name: '备注', required: false, example: '长期持有' }
    ]
  },

  onLoad(options) {
    const { accountId } = options
    
    if (!accountId) {
      wx.showToast({
        title: '账户ID不能为空',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ accountId })
  },

  // 选择文件
  selectFile() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['csv', 'xlsx', 'xls'],
      success: (res) => {
        const file = res.tempFiles[0]
        
        // 检查文件大小（限制5MB）
        if (file.size > 5 * 1024 * 1024) {
          wx.showToast({
            title: '文件大小不能超过5MB',
            icon: 'error'
          })
          return
        }
        
        // 检查文件类型
        const allowedTypes = ['csv', 'xlsx', 'xls']
        const fileExtension = file.name.split('.').pop().toLowerCase()
        
        if (!allowedTypes.includes(fileExtension)) {
          wx.showToast({
            title: '仅支持CSV和Excel文件',
            icon: 'error'
          })
          return
        }
        
        this.setData({
          fileInfo: {
            name: file.name,
            size: file.size,
            type: fileExtension,
            path: file.path
          }
        })
        
        // 解析文件
        this.parseFile(file)
      },
      fail: (error) => {
        console.error('选择文件失败:', error)
        wx.showToast({
          title: '选择文件失败',
          icon: 'error'
        })
      }
    })
  },

  // 解析文件
  async parseFile(file) {
    this.setData({ parsing: true })
    
    try {
      let data = []
      
      if (file.name.endsWith('.csv')) {
        data = await this.parseCSV(file.path)
      } else {
        data = await this.parseExcel(file.path)
      }
      
      // 验证数据
      const { validData, invalidData } = this.validateImportData(data)
      
      this.setData({
        parsedData: data,
        validData,
        invalidData,
        importStep: 'preview'
      })
      
      if (validData.length === 0) {
        wx.showModal({
          title: '数据验证失败',
          content: '没有找到有效的持仓数据，请检查文件格式和内容。',
          showCancel: false
        })
      }
      
    } catch (error) {
      console.error('解析文件失败:', error)
      wx.showToast({
        title: '文件解析失败',
        icon: 'error'
      })
    }
    
    this.setData({ parsing: false })
  },

  // 解析CSV文件
  async parseCSV(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().readFile({
        filePath,
        encoding: 'utf8',
        success: (res) => {
          try {
            const content = res.data
            const lines = content.split('\n').filter(line => line.trim())
            
            if (lines.length < 2) {
              throw new Error('文件内容不足')
            }
            
            // 解析表头
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
            
            // 解析数据行
            const data = []
            for (let i = 1; i < lines.length; i++) {
              const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
              const row = {}
              
              headers.forEach((header, index) => {
                row[header] = values[index] || ''
              })
              
              data.push(row)
            }
            
            resolve(data)
          } catch (error) {
            reject(error)
          }
        },
        fail: reject
      })
    })
  },

  // 解析Excel文件（简化版，实际应用中需要使用专门的库）
  async parseExcel(filePath) {
    // 由于微信小程序环境限制，这里提供一个简化的实现
    // 实际应用中建议使用云函数处理Excel文件
    throw new Error('Excel文件解析需要在云函数中处理，请使用CSV格式')
  },

  // 验证导入数据
  validateImportData(data) {
    const validData = []
    const invalidData = []
    
    data.forEach((row, index) => {
      const errors = []
      const rowData = {
        index: index + 1,
        symbol: (row['证券代码'] || row['symbol'] || '').toString().trim(),
        name: (row['证券名称'] || row['name'] || '').toString().trim(),
        type: (row['证券类型'] || row['type'] || 'stock').toString().trim(),
        quantity: (row['持仓数量'] || row['quantity'] || '').toString().trim(),
        costPrice: (row['成本价'] || row['costPrice'] || '').toString().trim(),
        notes: (row['备注'] || row['notes'] || '').toString().trim()
      }
      
      // 验证证券代码
      if (!rowData.symbol) {
        errors.push('证券代码不能为空')
      } else if (!validateSymbol(rowData.symbol, rowData.type)) {
        errors.push('证券代码格式不正确')
      }
      
      // 验证持仓数量
      if (!rowData.quantity) {
        errors.push('持仓数量不能为空')
      } else {
        const quantity = parseFloat(rowData.quantity)
        if (isNaN(quantity) || quantity <= 0) {
          errors.push('持仓数量必须大于0')
        }
      }
      
      // 验证成本价
      if (!rowData.costPrice) {
        errors.push('成本价不能为空')
      } else {
        const costPrice = parseFloat(rowData.costPrice)
        if (isNaN(costPrice) || costPrice <= 0) {
          errors.push('成本价必须大于0')
        }
      }
      
      // 验证证券类型
      if (!['stock', 'fund', 'bond'].includes(rowData.type)) {
        rowData.type = 'stock' // 默认为股票
      }
      
      if (errors.length === 0) {
        validData.push({
          ...rowData,
          quantity: parseFloat(rowData.quantity),
          costPrice: parseFloat(rowData.costPrice)
        })
      } else {
        invalidData.push({
          ...rowData,
          errors
        })
      }
    })
    
    return { validData, invalidData }
  },

  // 开始导入
  async startImport() {
    if (this.data.importing) return
    
    const { validData, accountId } = this.data
    
    if (validData.length === 0) {
      wx.showToast({
        title: '没有有效数据可导入',
        icon: 'error'
      })
      return
    }
    
    wx.showModal({
      title: '确认导入',
      content: `即将导入${validData.length}条持仓数据，是否继续？`,
      success: async (res) => {
        if (res.confirm) {
          await this.performImport()
        }
      }
    })
  },

  // 执行导入
  async performImport() {
    this.setData({
      importing: true,
      importStep: 'importing',
      importProgress: {
        total: this.data.validData.length,
        current: 0,
        success: 0,
        failed: 0,
        errors: []
      }
    })
    
    const { validData, accountId } = this.data
    const errors = []
    let success = 0
    let failed = 0
    
    // 逐条导入数据
    for (let i = 0; i < validData.length; i++) {
      const rowData = validData[i]
      
      try {
        // 检查是否已存在相同证券的持仓
        const existingPositions = await dataService.getPositions(accountId)
        if (existingPositions.success) {
          const duplicate = existingPositions.data.find(p => p.symbol === rowData.symbol)
          if (duplicate) {
            throw new Error(`证券${rowData.symbol}已存在持仓`)
          }
        }
        
        // 创建持仓数据
        const positionData = {
          accountId,
          symbol: rowData.symbol.toUpperCase(),
          name: rowData.name || rowData.symbol,
          type: rowData.type,
          quantity: rowData.quantity,
          costPrice: rowData.costPrice,
          notes: rowData.notes
        }
        
        const result = await dataService.addPosition(positionData)
        
        if (result.success) {
          success++
        } else {
          failed++
          errors.push({
            row: i + 1,
            symbol: rowData.symbol,
            error: result.error?.message || '导入失败'
          })
        }
        
      } catch (error) {
        failed++
        errors.push({
          row: i + 1,
          symbol: rowData.symbol,
          error: error.message
        })
      }
      
      // 更新进度
      this.setData({
        'importProgress.current': i + 1,
        'importProgress.success': success,
        'importProgress.failed': failed,
        'importProgress.errors': errors
      })
      
      // 添加延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    // 导入完成
    this.setData({
      importing: false,
      importStep: 'complete'
    })
    
    // 显示结果
    const message = `导入完成！成功：${success}条，失败：${failed}条`
    wx.showToast({
      title: message,
      icon: success > 0 ? 'success' : 'error',
      duration: 2000
    })
  },

  // 下载模板
  downloadTemplate() {
    const { templateColumns } = this.data
    
    // 生成CSV模板内容
    const headers = templateColumns.map(col => col.name).join(',')
    const examples = templateColumns.map(col => col.example || '').join(',')
    const csvContent = `${headers}\n${examples}`
    
    // 保存到临时文件
    const fs = wx.getFileSystemManager()
    const fileName = 'position_import_template.csv'
    const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`
    
    fs.writeFile({
      filePath,
      data: csvContent,
      encoding: 'utf8',
      success: () => {
        wx.showModal({
          title: '模板已生成',
          content: `模板文件已保存到：${fileName}`,
          showCancel: false,
          success: () => {
            // 可以通过分享或其他方式让用户获取模板
            wx.shareFileMessage({
              filePath,
              fileName,
              success: () => {
                wx.showToast({
                  title: '模板分享成功',
                  icon: 'success'
                })
              },
              fail: () => {
                wx.showToast({
                  title: '请手动创建CSV文件',
                  icon: 'none'
                })
              }
            })
          }
        })
      },
      fail: (error) => {
        console.error('生成模板失败:', error)
        wx.showToast({
          title: '生成模板失败',
          icon: 'error'
        })
      }
    })
  },

  // 重新选择文件
  reselectFile() {
    this.setData({
      importStep: 'select',
      fileInfo: { name: '', size: 0, type: '' },
      parsedData: [],
      validData: [],
      invalidData: []
    })
  },

  // 返回账户详情
  backToAccount() {
    wx.navigateBack()
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 获取导入进度百分比
  getImportProgress() {
    const { current, total } = this.data.importProgress
    return total > 0 ? Math.round((current / total) * 100) : 0
  }
})