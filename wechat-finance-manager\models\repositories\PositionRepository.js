/**
 * 持仓数据仓库
 * 负责持仓数据的数据库操作
 */

const Position = require('../Position');
const { COLLECTION_NAME, validateSymbol } = require('../collections/positions');

class PositionRepository {
  constructor(db) {
    this.db = db;
    this.collection = db.collection(COLLECTION_NAME);
  }

  /**
   * 创建新持仓
   * @param {Position} position 持仓实例
   * @returns {Promise<Object>} 创建结果
   */
  async create(position) {
    try {
      // 验证持仓数据
      const validation = position.validate();
      if (!validation.isValid) {
        throw new Error(`持仓数据验证失败: ${validation.errors.join(', ')}`);
      }

      // 验证证券代码格式
      if (!validateSymbol(position.symbol, position.type)) {
        throw new Error('证券代码格式无效');
      }

      // 检查持仓是否已存在
      const existingPosition = await this.findByAccountAndSymbol(
        position.accountId, 
        position.symbol
      );
      if (existingPosition) {
        throw new Error('该账户已存在此证券的持仓');
      }

      // 重新计算相关数值
      position.recalculate();

      // 保存到数据库
      const result = await this.collection.add({
        data: position.toDatabase()
      });

      return {
        success: true,
        _id: result._id,
        message: '持仓创建成功'
      };
    } catch (error) {
      console.error('创建持仓失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 根据账户ID和证券代码查找持仓
   * @param {string} accountId 账户ID
   * @param {string} symbol 证券代码
   * @returns {Promise<Position|null>} 持仓实例或null
   */
  async findByAccountAndSymbol(accountId, symbol) {
    try {
      const result = await this.collection.where({
        accountId: accountId,
        symbol: symbol
      }).get();

      if (result.data.length === 0) {
        return null;
      }

      return Position.fromDatabase(result.data[0]);
    } catch (error) {
      console.error('查找持仓失败:', error);
      return null;
    }
  }

  /**
   * 根据ID查找持仓
   * @param {string} positionId 持仓ID
   * @returns {Promise<Position|null>} 持仓实例或null
   */
  async findById(positionId) {
    try {
      const result = await this.collection.doc(positionId).get();

      if (!result.data) {
        return null;
      }

      return Position.fromDatabase(result.data);
    } catch (error) {
      console.error('查找持仓失败:', error);
      return null;
    }
  }

  /**
   * 根据账户ID查找所有持仓
   * @param {string} accountId 账户ID
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 持仓列表
   */
  async findByAccountId(accountId, options = {}) {
    try {
      const { 
        type = null, 
        isActive = null, 
        limit = 100, 
        skip = 0,
        orderBy = 'marketValue',
        order = 'desc'
      } = options;

      let query = this.collection.where({
        accountId: accountId
      });

      // 添加证券类型筛选
      if (type) {
        query = query.where({
          type: type
        });
      }

      // 添加激活状态筛选
      if (isActive !== null) {
        query = query.where({
          isActive: isActive
        });
      }

      // 添加排序
      if (order === 'desc') {
        query = query.orderBy(orderBy, 'desc');
      } else {
        query = query.orderBy(orderBy, 'asc');
      }

      // 添加分页
      query = query.skip(skip).limit(limit);

      const result = await query.get();

      return result.data.map(positionData => Position.fromDatabase(positionData));
    } catch (error) {
      console.error('查找账户持仓失败:', error);
      return [];
    }
  }

  /**
   * 根据用户ID查找所有持仓
   * @param {string} userId 用户ID
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 持仓列表
   */
  async findByUserId(userId, options = {}) {
    try {
      const { 
        type = null, 
        isActive = null, 
        limit = 200, 
        skip = 0,
        orderBy = 'marketValue',
        order = 'desc'
      } = options;

      let query = this.collection.where({
        userId: userId
      });

      // 添加证券类型筛选
      if (type) {
        query = query.where({
          type: type
        });
      }

      // 添加激活状态筛选
      if (isActive !== null) {
        query = query.where({
          isActive: isActive
        });
      }

      // 添加排序
      if (order === 'desc') {
        query = query.orderBy(orderBy, 'desc');
      } else {
        query = query.orderBy(orderBy, 'asc');
      }

      // 添加分页
      query = query.skip(skip).limit(limit);

      const result = await query.get();

      return result.data.map(positionData => Position.fromDatabase(positionData));
    } catch (error) {
      console.error('查找用户持仓失败:', error);
      return [];
    }
  }

  /**
   * 更新持仓信息
   * @param {string} positionId 持仓ID
   * @param {Object} updateData 更新数据
   * @param {string} userId 用户ID（用于权限验证）
   * @returns {Promise<Object>} 更新结果
   */
  async update(positionId, updateData, userId) {
    try {
      // 先获取现有持仓数据
      const existingPosition = await this.findById(positionId);
      if (!existingPosition) {
        throw new Error('持仓不存在');
      }

      // 检查权限
      if (!existingPosition.checkPermission(userId)) {
        throw new Error('无权限操作此持仓');
      }

      // 如果更新证券代码，检查是否重复
      if (updateData.symbol && updateData.symbol !== existingPosition.symbol) {
        const duplicatePosition = await this.findByAccountAndSymbol(
          existingPosition.accountId, 
          updateData.symbol
        );
        if (duplicatePosition) {
          throw new Error('该账户已存在此证券的持仓');
        }

        // 验证新证券代码格式
        const newType = updateData.type || existingPosition.type;
        if (!validateSymbol(updateData.symbol, newType)) {
          throw new Error('证券代码格式无效');
        }
      }

      // 更新持仓数据
      existingPosition.update(updateData);

      // 验证更新后的数据
      const validation = existingPosition.validate();
      if (!validation.isValid) {
        throw new Error(`持仓数据验证失败: ${validation.errors.join(', ')}`);
      }

      // 保存到数据库
      await this.collection.doc(positionId).update({
        data: existingPosition.toDatabase()
      });

      return {
        success: true,
        message: '持仓信息更新成功'
      };
    } catch (error) {
      console.error('更新持仓失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 删除持仓
   * @param {string} positionId 持仓ID
   * @param {string} userId 用户ID（用于权限验证）
   * @returns {Promise<Object>} 删除结果
   */
  async delete(positionId, userId) {
    try {
      // 检查持仓是否存在
      const existingPosition = await this.findById(positionId);
      if (!existingPosition) {
        throw new Error('持仓不存在');
      }

      // 检查权限
      if (!existingPosition.checkPermission(userId)) {
        throw new Error('无权限操作此持仓');
      }

      // 删除持仓数据
      await this.collection.doc(positionId).remove();

      return {
        success: true,
        message: '持仓删除成功'
      };
    } catch (error) {
      console.error('删除持仓失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 批量更新持仓价格
   * @param {Array} priceUpdates 价格更新数据 [{symbol, currentPrice, dailyChange, dailyChangePercent}]
   * @returns {Promise<Object>} 更新结果
   */
  async batchUpdatePrices(priceUpdates) {
    try {
      const updatePromises = priceUpdates.map(async (priceUpdate) => {
        const { symbol, currentPrice, dailyChange = 0, dailyChangePercent = 0 } = priceUpdate;
        
        // 查找所有该证券的持仓
        const positions = await this.collection.where({
          symbol: symbol,
          isActive: true
        }).get();

        // 更新每个持仓的价格
        const positionUpdatePromises = positions.data.map(async (positionData) => {
          const position = Position.fromDatabase(positionData);
          position.updatePrice(currentPrice, dailyChange, dailyChangePercent);
          
          return this.collection.doc(position._id).update({
            data: {
              currentPrice: position.currentPrice,
              marketValue: position.marketValue,
              unrealizedPnL: position.unrealizedPnL,
              unrealizedPnLPercent: position.unrealizedPnLPercent,
              dailyChange: position.dailyChange,
              dailyChangePercent: position.dailyChangePercent,
              priceUpdateTime: position.priceUpdateTime,
              updatedAt: position.updatedAt
            }
          });
        });

        return Promise.all(positionUpdatePromises);
      });

      await Promise.all(updatePromises);

      return {
        success: true,
        message: `批量更新${priceUpdates.length}个证券的价格成功`
      };
    } catch (error) {
      console.error('批量更新持仓价格失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 统计持仓数量
   * @param {string} accountId 账户ID
   * @param {Object} filters 筛选条件
   * @returns {Promise<number>} 持仓数量
   */
  async countByAccountId(accountId, filters = {}) {
    try {
      let query = this.collection.where({
        accountId: accountId
      });

      // 添加筛选条件
      if (filters.type) {
        query = query.where({
          type: filters.type
        });
      }

      if (filters.isActive !== undefined) {
        query = query.where({
          isActive: filters.isActive
        });
      }

      const result = await query.count();
      return result.total;
    } catch (error) {
      console.error('统计持仓数量失败:', error);
      return 0;
    }
  }

  /**
   * 获取账户持仓统计
   * @param {string} accountId 账户ID
   * @returns {Promise<Object>} 持仓统计信息
   */
  async getAccountPositionSummary(accountId) {
    try {
      const positions = await this.findByAccountId(accountId, { isActive: true });
      
      const summary = {
        totalPositions: positions.length,
        stockPositions: 0,
        fundPositions: 0,
        bondPositions: 0,
        totalMarketValue: 0,
        totalCost: 0,
        totalUnrealizedPnL: 0,
        totalUnrealizedPnLPercent: 0,
        profitPositions: 0,
        lossPositions: 0,
        breakEvenPositions: 0
      };

      positions.forEach(position => {
        // 按类型统计
        if (position.type === 'stock') {
          summary.stockPositions++;
        } else if (position.type === 'fund') {
          summary.fundPositions++;
        } else if (position.type === 'bond') {
          summary.bondPositions++;
        }

        // 累计数值
        summary.totalMarketValue += position.marketValue;
        summary.totalCost += position.totalCost;
        summary.totalUnrealizedPnL += position.unrealizedPnL;

        // 按盈亏状态统计
        if (position.unrealizedPnL > 0) {
          summary.profitPositions++;
        } else if (position.unrealizedPnL < 0) {
          summary.lossPositions++;
        } else {
          summary.breakEvenPositions++;
        }
      });

      // 计算总的未实现盈亏百分比
      if (summary.totalCost > 0) {
        summary.totalUnrealizedPnLPercent = (summary.totalUnrealizedPnL / summary.totalCost) * 100;
      }

      return summary;
    } catch (error) {
      console.error('获取账户持仓统计失败:', error);
      return {
        totalPositions: 0,
        stockPositions: 0,
        fundPositions: 0,
        bondPositions: 0,
        totalMarketValue: 0,
        totalCost: 0,
        totalUnrealizedPnL: 0,
        totalUnrealizedPnLPercent: 0,
        profitPositions: 0,
        lossPositions: 0,
        breakEvenPositions: 0
      };
    }
  }

  /**
   * 获取需要更新价格的证券列表
   * @param {Date} lastUpdateTime 最后更新时间
   * @returns {Promise<Array>} 证券代码列表
   */
  async getSymbolsForPriceUpdate(lastUpdateTime = null) {
    try {
      let query = this.collection.where({
        isActive: true
      });

      // 如果指定了最后更新时间，只获取需要更新的持仓
      if (lastUpdateTime) {
        query = query.where({
          priceUpdateTime: this.db.command.lt(lastUpdateTime)
        });
      }

      const result = await query.field({
        symbol: true,
        type: true
      }).get();

      // 去重并返回证券代码列表
      const symbolSet = new Set();
      result.data.forEach(position => {
        symbolSet.add(position.symbol);
      });

      return Array.from(symbolSet);
    } catch (error) {
      console.error('获取需要更新价格的证券列表失败:', error);
      return [];
    }
  }
}

module.exports = PositionRepository;