// utils/calculator.js

/**
 * 交易费用计算器
 */
class FeeCalculator {
  constructor(config = {}) {
    // 默认费率配置
    this.config = {
      commissionRate: 0.0003, // 佣金费率 0.03%
      minCommission: 5,       // 最低佣金 5元
      stampTaxRate: 0.001,    // 印花税费率 0.1% (仅卖出)
      transferFeeRate: 0.00002, // 过户费费率 0.002%
      minTransferFee: 1,      // 最低过户费 1元
      ...config
    }
  }

  /**
   * 计算买入费用
   */
  calculateBuyFees(amount) {
    const commission = Math.max(amount * this.config.commissionRate, this.config.minCommission)
    const transferFee = Math.max(amount * this.config.transferFeeRate, this.config.minTransferFee)
    
    return {
      commission: this.round(commission),
      stampTax: 0, // 买入无印花税
      transferFee: this.round(transferFee),
      total: this.round(commission + transferFee)
    }
  }

  /**
   * 计算卖出费用
   */
  calculateSellFees(amount) {
    const commission = Math.max(amount * this.config.commissionRate, this.config.minCommission)
    const stampTax = amount * this.config.stampTaxRate
    const transferFee = Math.max(amount * this.config.transferFeeRate, this.config.minTransferFee)
    
    return {
      commission: this.round(commission),
      stampTax: this.round(stampTax),
      transferFee: this.round(transferFee),
      total: this.round(commission + stampTax + transferFee)
    }
  }

  /**
   * 四舍五入到2位小数
   */
  round(value) {
    return Math.round(value * 100) / 100
  }
}

/**
 * 盈亏计算器
 */
class PnLCalculator {
  /**
   * 计算持仓盈亏
   */
  static calculatePositionPnL(quantity, costPrice, currentPrice) {
    const costAmount = quantity * costPrice
    const currentAmount = quantity * currentPrice
    const unrealizedPnL = currentAmount - costAmount
    const unrealizedPnLPercent = (unrealizedPnL / costAmount) * 100

    return {
      costAmount: this.round(costAmount),
      currentAmount: this.round(currentAmount),
      unrealizedPnL: this.round(unrealizedPnL),
      unrealizedPnLPercent: this.round(unrealizedPnLPercent)
    }
  }

  /**
   * 计算交易盈亏
   */
  static calculateTradePnL(buyPrice, sellPrice, quantity, buyFees, sellFees) {
    const buyAmount = quantity * buyPrice + buyFees
    const sellAmount = quantity * sellPrice - sellFees
    const realizedPnL = sellAmount - buyAmount
    const realizedPnLPercent = (realizedPnL / buyAmount) * 100

    return {
      buyAmount: this.round(buyAmount),
      sellAmount: this.round(sellAmount),
      realizedPnL: this.round(realizedPnL),
      realizedPnLPercent: this.round(realizedPnLPercent)
    }
  }

  /**
   * 计算年化收益率
   */
  static calculateAnnualizedReturn(totalReturn, holdingDays) {
    if (holdingDays <= 0) return 0
    const annualizedReturn = (Math.pow(1 + totalReturn, 365 / holdingDays) - 1) * 100
    return this.round(annualizedReturn)
  }

  /**
   * 四舍五入到2位小数
   */
  static round(value) {
    return Math.round(value * 100) / 100
  }
}

/**
 * 资产计算器
 */
class AssetCalculator {
  /**
   * 计算账户总资产
   */
  static calculateAccountAssets(positions, prices) {
    let totalValue = 0
    let totalCost = 0

    positions.forEach(position => {
      const currentPrice = prices[position.symbol]?.price || position.costPrice
      const marketValue = position.quantity * currentPrice
      const costValue = position.quantity * position.costPrice

      totalValue += marketValue
      totalCost += costValue
    })

    const totalPnL = totalValue - totalCost
    const totalPnLPercent = totalCost > 0 ? (totalPnL / totalCost) * 100 : 0

    return {
      totalValue: this.round(totalValue),
      totalCost: this.round(totalCost),
      totalPnL: this.round(totalPnL),
      totalPnLPercent: this.round(totalPnLPercent)
    }
  }

  /**
   * 计算日变化
   */
  static calculateDailyChange(positions, prices, previousPrices) {
    let currentValue = 0
    let previousValue = 0

    positions.forEach(position => {
      const currentPrice = prices[position.symbol]?.price || position.costPrice
      const previousPrice = previousPrices[position.symbol]?.price || currentPrice

      currentValue += position.quantity * currentPrice
      previousValue += position.quantity * previousPrice
    })

    const dailyChange = currentValue - previousValue
    const dailyChangePercent = previousValue > 0 ? (dailyChange / previousValue) * 100 : 0

    return {
      currentValue: this.round(currentValue),
      previousValue: this.round(previousValue),
      dailyChange: this.round(dailyChange),
      dailyChangePercent: this.round(dailyChangePercent)
    }
  }

  /**
   * 四舍五入到2位小数
   */
  static round(value) {
    return Math.round(value * 100) / 100
  }
}

/**
 * 基金计算器
 */
class FundCalculator {
  /**
   * 计算基金持仓价值
   */
  static calculateFundValue(shares, nav) {
    return this.round(shares * nav)
  }

  /**
   * 计算基金收益
   */
  static calculateFundReturn(shares, costNav, currentNav) {
    const costValue = shares * costNav
    const currentValue = shares * currentNav
    const totalReturn = currentValue - costValue
    const returnPercent = (totalReturn / costValue) * 100

    return {
      costValue: this.round(costValue),
      currentValue: this.round(currentValue),
      totalReturn: this.round(totalReturn),
      returnPercent: this.round(returnPercent)
    }
  }

  /**
   * 四舍五入到2位小数
   */
  static round(value) {
    return Math.round(value * 100) / 100
  }
}

module.exports = {
  FeeCalculator,
  PnLCalculator,
  AssetCalculator,
  FundCalculator
}