// components/account-card/account-card.js
Component({
  properties: {
    account: {
      type: Object,
      value: {}
    }
  },

  data: {
    showMenu: false
  },

  methods: {
    // 点击账户卡片
    onCardTap() {
      this.triggerEvent('cardtap', {
        accountId: this.properties.account._id
      })
    },

    // 显示操作菜单
    showActionMenu(e) {
      e.stopPropagation()
      this.setData({
        showMenu: true
      })
    },

    // 隐藏操作菜单
    hideActionMenu() {
      this.setData({
        showMenu: false
      })
    },

    // 编辑账户
    editAccount(e) {
      e.stopPropagation()
      this.hideActionMenu()
      this.triggerEvent('edit', {
        account: this.properties.account
      })
    },

    // 删除账户
    deleteAccount(e) {
      e.stopPropagation()
      this.hideActionMenu()
      
      wx.showModal({
        title: '确认删除',
        content: `确定要删除账户"${this.properties.account.brokerName}"吗？删除后无法恢复。`,
        confirmText: '删除',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', {
              accountId: this.properties.account._id
            })
          }
        }
      })
    },

    // 格式化数值显示
    formatValue(value) {
      if (!value) return '0.00'
      return parseFloat(value).toFixed(2)
    },

    // 格式化账户号码（隐藏中间部分）
    formatAccountNumber(accountNumber) {
      if (!accountNumber || accountNumber.length < 8) return accountNumber
      const start = accountNumber.substring(0, 4)
      const end = accountNumber.substring(accountNumber.length - 4)
      return `${start}****${end}`
    }
  }
})