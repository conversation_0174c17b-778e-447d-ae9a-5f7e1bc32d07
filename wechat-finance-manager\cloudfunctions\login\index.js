// cloudfunctions/login/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  try {
    // 获取用户信息
    const { OPENID, APPID, UNIONID } = wxContext

    // 查询用户是否已存在
    const userResult = await db.collection('users').where({
      openid: OPENID
    }).get()

    let userData
    
    if (userResult.data.length === 0) {
      // 新用户，创建用户记录
      const createResult = await db.collection('users').add({
        data: {
          openid: OPENID,
          appid: APPID,
          unionid: UNIONID,
          theme: 'light',
          settings: {
            autoRefresh: true,
            notifications: true,
            defaultTimeRange: 'month'
          },
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })

      userData = {
        _id: createResult._id,
        openid: OPENID,
        appid: APPID,
        unionid: UNIONID,
        theme: 'light',
        settings: {
          autoRefresh: true,
          notifications: true,
          defaultTimeRange: 'month'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    } else {
      // 已存在用户，更新最后登录时间
      userData = userResult.data[0]
      
      await db.collection('users').doc(userData._id).update({
        data: {
          lastLoginAt: new Date(),
          updatedAt: new Date()
        }
      })
    }

    return {
      success: true,
      data: {
        openid: OPENID,
        appid: APPID,
        unionid: UNIONID,
        ...userData
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}