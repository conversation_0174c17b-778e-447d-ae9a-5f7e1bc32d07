/**
 * 用户集合配置
 * 定义用户集合的数据库结构、索引和权限规则
 */

const COLLECTION_NAME = 'users';

/**
 * 用户集合的数据库索引配置
 */
const INDEXES = [
  {
    // openid唯一索引，用于快速查找用户
    keys: { openid: 1 },
    options: { 
      unique: true,
      name: 'openid_unique'
    }
  },
  {
    // 创建时间索引，用于按时间排序
    keys: { createdAt: -1 },
    options: { 
      name: 'created_at_desc'
    }
  },
  {
    // 更新时间索引，用于查找最近活跃用户
    keys: { updatedAt: -1 },
    options: { 
      name: 'updated_at_desc'
    }
  }
];

/**
 * 数据库权限规则
 * 确保用户只能访问自己的数据
 */
const PERMISSION_RULES = {
  read: "auth.openid == resource.data.openid",
  write: "auth.openid == resource.data.openid",
  create: "auth.openid == resource.data.openid",
  delete: "auth.openid == resource.data.openid"
};

/**
 * 数据验证规则
 */
const VALIDATION_SCHEMA = {
  required: ['openid'],
  properties: {
    openid: {
      type: 'string',
      minLength: 1,
      maxLength: 100
    },
    nickName: {
      type: 'string',
      maxLength: 50
    },
    avatarUrl: {
      type: 'string',
      maxLength: 500
    },
    theme: {
      type: 'string',
      enum: ['light', 'dark']
    },
    settings: {
      type: 'object',
      properties: {
        autoRefresh: { type: 'boolean' },
        notifications: { type: 'boolean' },
        defaultTimeRange: {
          type: 'string',
          enum: ['week', 'month', 'quarter', 'half-year', 'year', 'all']
        }
      }
    },
    createdAt: { type: 'date' },
    updatedAt: { type: 'date' }
  }
};

/**
 * 初始化用户集合
 * 创建索引和设置权限规则
 */
async function initializeCollection(db) {
  try {
    const collection = db.collection(COLLECTION_NAME);
    
    // 创建索引
    for (const index of INDEXES) {
      await collection.createIndex(index.keys, index.options);
      console.log(`Created index: ${index.options.name}`);
    }
    
    console.log(`User collection initialized successfully`);
    return true;
  } catch (error) {
    console.error('Failed to initialize user collection:', error);
    return false;
  }
}

module.exports = {
  COLLECTION_NAME,
  INDEXES,
  PERMISSION_RULES,
  VALIDATION_SCHEMA,
  initializeCollection
};