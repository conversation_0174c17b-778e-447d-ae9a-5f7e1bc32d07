/**
 * 账户数据模型
 * 负责证券账户和基金账户的数据结构定义、验证和管理
 */

class Account {
  constructor(data = {}) {
    this._id = data._id || null;
    this.userId = data.userId || '';
    this.brokerName = data.brokerName || '';
    this.accountNumber = data.accountNumber || '';
    this.accountType = data.accountType || 'securities'; // securities/fund
    this.totalValue = data.totalValue || 0;
    this.dailyChange = data.dailyChange || 0;
    this.dailyChangePercent = data.dailyChangePercent || 0;
    this.positions = data.positions || [];
    this.isActive = data.isActive !== undefined ? data.isActive : true;
    this.displayName = data.displayName || '';
    this.description = data.description || '';
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  /**
   * 验证账户数据
   * @returns {Object} 验证结果 {isValid: boolean, errors: Array}
   */
  validate() {
    const errors = [];

    // 验证用户ID
    if (!this.userId || typeof this.userId !== 'string') {
      errors.push('用户ID是必填字段且必须为字符串');
    }

    // 验证券商名称
    if (!this.brokerName || typeof this.brokerName !== 'string') {
      errors.push('券商名称是必填字段且必须为字符串');
    }

    if (this.brokerName.length > 50) {
      errors.push('券商名称长度不能超过50个字符');
    }

    // 验证账户号码
    if (!this.accountNumber || typeof this.accountNumber !== 'string') {
      errors.push('账户号码是必填字段且必须为字符串');
    }

    // 验证账户号码格式（10-20位数字）
    if (!/^\d{10,20}$/.test(this.accountNumber)) {
      errors.push('账户号码必须为10-20位数字');
    }

    // 验证账户类型
    if (!['securities', 'fund'].includes(this.accountType)) {
      errors.push('账户类型只能是securities或fund');
    }

    // 验证数值字段
    if (typeof this.totalValue !== 'number' || this.totalValue < 0) {
      errors.push('总价值必须为非负数');
    }

    if (typeof this.dailyChange !== 'number') {
      errors.push('日变化必须为数字');
    }

    if (typeof this.dailyChangePercent !== 'number') {
      errors.push('日变化百分比必须为数字');
    }

    // 验证布尔字段
    if (typeof this.isActive !== 'boolean') {
      errors.push('激活状态必须为布尔值');
    }

    // 验证可选字段
    if (this.displayName && typeof this.displayName !== 'string') {
      errors.push('显示名称必须为字符串');
    }

    if (this.displayName && this.displayName.length > 100) {
      errors.push('显示名称长度不能超过100个字符');
    }

    if (this.description && typeof this.description !== 'string') {
      errors.push('描述必须为字符串');
    }

    if (this.description && this.description.length > 500) {
      errors.push('描述长度不能超过500个字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 转换为数据库存储格式
   * @returns {Object} 数据库存储对象
   */
  toDatabase() {
    const data = {
      userId: this.userId,
      brokerName: this.brokerName,
      accountNumber: this.accountNumber,
      accountType: this.accountType,
      totalValue: this.totalValue,
      dailyChange: this.dailyChange,
      dailyChangePercent: this.dailyChangePercent,
      positions: this.positions,
      isActive: this.isActive,
      displayName: this.displayName,
      description: this.description,
      updatedAt: new Date()
    };

    // 如果是新账户，添加创建时间
    if (!this._id) {
      data.createdAt = new Date();
    }

    return data;
  }

  /**
   * 从数据库数据创建账户实例
   * @param {Object} dbData 数据库数据
   * @returns {Account} 账户实例
   */
  static fromDatabase(dbData) {
    return new Account({
      _id: dbData._id,
      userId: dbData.userId,
      brokerName: dbData.brokerName,
      accountNumber: dbData.accountNumber,
      accountType: dbData.accountType,
      totalValue: dbData.totalValue,
      dailyChange: dbData.dailyChange,
      dailyChangePercent: dbData.dailyChangePercent,
      positions: dbData.positions,
      isActive: dbData.isActive,
      displayName: dbData.displayName,
      description: dbData.description,
      createdAt: dbData.createdAt,
      updatedAt: dbData.updatedAt
    });
  }

  /**
   * 更新账户信息
   * @param {Object} updateData 更新数据
   */
  update(updateData) {
    if (updateData.brokerName !== undefined) {
      this.brokerName = updateData.brokerName;
    }
    if (updateData.accountNumber !== undefined) {
      this.accountNumber = updateData.accountNumber;
    }
    if (updateData.accountType !== undefined) {
      this.accountType = updateData.accountType;
    }
    if (updateData.totalValue !== undefined) {
      this.totalValue = updateData.totalValue;
    }
    if (updateData.dailyChange !== undefined) {
      this.dailyChange = updateData.dailyChange;
    }
    if (updateData.dailyChangePercent !== undefined) {
      this.dailyChangePercent = updateData.dailyChangePercent;
    }
    if (updateData.positions !== undefined) {
      this.positions = updateData.positions;
    }
    if (updateData.isActive !== undefined) {
      this.isActive = updateData.isActive;
    }
    if (updateData.displayName !== undefined) {
      this.displayName = updateData.displayName;
    }
    if (updateData.description !== undefined) {
      this.description = updateData.description;
    }
    this.updatedAt = new Date();
  }

  /**
   * 获取账户显示信息
   * @returns {Object} 账户显示信息
   */
  getDisplayInfo() {
    return {
      _id: this._id,
      brokerName: this.brokerName,
      accountNumber: this.maskAccountNumber(),
      accountType: this.accountType,
      accountTypeName: this.getAccountTypeName(),
      totalValue: this.totalValue,
      dailyChange: this.dailyChange,
      dailyChangePercent: this.dailyChangePercent,
      positionCount: this.positions.length,
      displayName: this.displayName || this.getDefaultDisplayName(),
      description: this.description,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * 获取账户类型中文名称
   * @returns {string} 账户类型中文名称
   */
  getAccountTypeName() {
    const typeNames = {
      securities: '证券账户',
      fund: '基金账户'
    };
    return typeNames[this.accountType] || '未知类型';
  }

  /**
   * 获取默认显示名称
   * @returns {string} 默认显示名称
   */
  getDefaultDisplayName() {
    return `${this.brokerName}-${this.maskAccountNumber()}`;
  }

  /**
   * 掩码账户号码（隐藏中间部分）
   * @returns {string} 掩码后的账户号码
   */
  maskAccountNumber() {
    if (!this.accountNumber || this.accountNumber.length < 8) {
      return this.accountNumber;
    }

    const start = this.accountNumber.substring(0, 4);
    const end = this.accountNumber.substring(this.accountNumber.length - 4);
    const middle = '*'.repeat(this.accountNumber.length - 8);
    
    return `${start}${middle}${end}`;
  }

  /**
   * 计算账户统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    const totalCost = this.positions.reduce((sum, pos) => sum + (pos.quantity * pos.costPrice), 0);
    const totalMarketValue = this.totalValue;
    const totalUnrealizedPnL = totalMarketValue - totalCost;
    const totalUnrealizedPnLPercent = totalCost > 0 ? (totalUnrealizedPnL / totalCost) * 100 : 0;

    return {
      positionCount: this.positions.length,
      totalCost,
      totalMarketValue,
      totalUnrealizedPnL,
      totalUnrealizedPnLPercent,
      dailyChange: this.dailyChange,
      dailyChangePercent: this.dailyChangePercent
    };
  }

  /**
   * 添加持仓
   * @param {Object} position 持仓数据
   */
  addPosition(position) {
    this.positions.push(position);
    this.updatedAt = new Date();
  }

  /**
   * 移除持仓
   * @param {string} positionId 持仓ID
   */
  removePosition(positionId) {
    this.positions = this.positions.filter(pos => pos._id !== positionId);
    this.updatedAt = new Date();
  }

  /**
   * 更新持仓
   * @param {string} positionId 持仓ID
   * @param {Object} updateData 更新数据
   */
  updatePosition(positionId, updateData) {
    const positionIndex = this.positions.findIndex(pos => pos._id === positionId);
    if (positionIndex !== -1) {
      this.positions[positionIndex] = { ...this.positions[positionIndex], ...updateData };
      this.updatedAt = new Date();
    }
  }

  /**
   * 检查账户权限
   * @param {string} userId 用户ID
   * @returns {boolean} 是否有权限
   */
  checkPermission(userId) {
    return this.userId === userId;
  }
}

module.exports = Account;