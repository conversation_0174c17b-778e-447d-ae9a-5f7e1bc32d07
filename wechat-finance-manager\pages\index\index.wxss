/* pages/index/index.wxss */
.container {
  padding: 20rpx;
  background-color: var(--background-secondary);
  min-height: 100vh;
}

/* 快捷操作区域 */
.quick-actions-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-all {
  font-size: 26rpx;
  color: var(--primary-color);
  font-weight: 400;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.action-item {
  background: var(--background-primary);
  border-radius: var(--border-radius);
  padding: 40rpx 20rpx;
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: all 0.2s ease;
}

.action-item:active {
  transform: scale(0.95);
  background: var(--background-tertiary);
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.action-text {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 账户概览区域 */
.accounts-section {
  margin-bottom: 40rpx;
}

.accounts-preview {
  background: var(--background-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.account-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s ease;
}

.account-item:last-child {
  border-bottom: none;
}

.account-item:active {
  background-color: var(--background-secondary);
}

.account-info {
  flex: 1;
}

.account-name {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.account-number {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.account-value {
  text-align: right;
}

.value-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.value-change {
  font-size: 24rpx;
  font-weight: 500;
}

.value-change.positive {
  color: var(--success-color);
}

.value-change.negative {
  color: var(--error-color);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: var(--background-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 40rpx;
  line-height: 1.5;
}