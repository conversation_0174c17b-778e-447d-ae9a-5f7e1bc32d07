/* components/account-card/account-card.wxss */
.account-card {
  position: relative;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.account-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.card-content {
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.account-info {
  flex: 1;
}

.broker-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.account-number {
  font-size: 26rpx;
  color: #666;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.account-value {
  text-align: right;
  margin-right: 60rpx;
}

.total-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.daily-change {
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.daily-change.positive {
  color: #34C759;
}

.daily-change.negative {
  color: #FF3B30;
}

.change-percent {
  margin-left: 8rpx;
  font-size: 22rpx;
}

.action-button {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
}

.action-button:active {
  background: rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 32rpx;
  color: #666;
  font-weight: bold;
  transform: rotate(90deg);
}

/* 操作菜单样式 */
.action-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.action-menu.show {
  opacity: 1;
  visibility: visible;
}

.menu-content {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx 0;
  min-width: 200rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.action-menu.show .menu-content {
  transform: scale(1);
}

.menu-item {
  padding: 24rpx 40rpx;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.menu-item:active {
  background: rgba(0, 0, 0, 0.05);
}

.menu-item.delete:active {
  background: rgba(255, 59, 48, 0.1);
}

.menu-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.menu-text {
  font-size: 30rpx;
  color: #333;
}

.menu-item.delete .menu-text {
  color: #FF3B30;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .account-card {
    background: #1c1c1e;
  }
  
  .broker-name {
    color: #ffffff;
  }
  
  .account-number {
    color: #8e8e93;
  }
  
  .total-value {
    color: #ffffff;
  }
  
  .action-button {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .action-icon {
    color: #8e8e93;
  }
  
  .menu-content {
    background: #2c2c2e;
  }
  
  .menu-text {
    color: #ffffff;
  }
}