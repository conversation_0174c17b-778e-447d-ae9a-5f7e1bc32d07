// custom-tab-bar/index.js
const pageTransition = require('../utils/pageTransition')
const ThemeService = require('../services/ThemeService')

Component({
  data: {
    selected: 0,
    theme: 'light',
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页"
      },
      {
        pagePath: "/pages/accounts/accounts", 
        text: "账户"
      },
      {
        pagePath: "/pages/transactions/transactions",
        text: "交易"
      },
      {
        pagePath: "/pages/funds/funds",
        text: "基金"
      },
      {
        pagePath: "/pages/settings/settings",
        text: "设置"
      }
    ]
  },

  lifetimes: {
    attached() {
      this.updateSelected()
      this.updateTheme()
      this.setupThemeListener()
    },

    detached() {
      this.removeThemeListener()
    }
  },

  methods: {
    switchTab(e) {
      const { index, path } = e.currentTarget.dataset
      const selectedIndex = parseInt(index)
      
      if (selectedIndex === this.data.selected) {
        return
      }

      // 使用页面过渡动画
      pageTransition.switchTabWithTransition(path, {
        transition: 'fade'
      }).then(() => {
        this.setData({
          selected: selectedIndex
        })
        
        // 通知其他页面更新 tabbar 状态
        this.updateTabBarOnAllPages(selectedIndex)
      }).catch((error) => {
        console.error('Tab switch failed:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      })
    },

    updateSelected() {
      const pages = getCurrentPages()
      if (pages.length === 0) return
      
      const currentPage = pages[pages.length - 1]
      const currentPath = '/' + currentPage.route
      
      const selectedIndex = this.data.list.findIndex(item => item.pagePath === currentPath)
      
      if (selectedIndex !== -1) {
        this.setData({
          selected: selectedIndex
        })
      }
    },

    updateTheme() {
      try {
        const theme = ThemeService.getCurrentTheme()
        this.setData({ theme })
      } catch (e) {
        console.error('Failed to load theme:', e)
      }
    },

    // 设置主题监听器
    setupThemeListener() {
      this.themeChangeHandler = (theme) => {
        this.setData({ theme })
      }
      ThemeService.addThemeListener(this.themeChangeHandler)
    },

    // 移除主题监听器
    removeThemeListener() {
      if (this.themeChangeHandler) {
        ThemeService.removeThemeListener(this.themeChangeHandler)
      }
    },

    updateTabBarOnAllPages(selectedIndex) {
      const pages = getCurrentPages()
      pages.forEach(page => {
        if (page.updateTabBar && typeof page.updateTabBar === 'function') {
          page.updateTabBar(selectedIndex)
        }
      })
    }
  }
})