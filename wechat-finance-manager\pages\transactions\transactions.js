// pages/transactions/transactions.js
Page({
  data: {
    transactions: [],
    loading: true
  },

  onLoad() {
    console.log('Transactions page loaded')
    this.loadTransactions()
  },

  onShow() {
    this.loadTransactions()
  },

  loadTransactions() {
    this.setData({
      loading: true
    })

    // TODO: 实现交易记录加载逻辑
    setTimeout(() => {
      this.setData({
        transactions: [],
        loading: false
      })
    }, 1000)
  },

  // 添加交易记录
  addTransaction() {
    wx.navigateTo({
      url: '/pages/add-transaction/add-transaction'
    })
  }
})