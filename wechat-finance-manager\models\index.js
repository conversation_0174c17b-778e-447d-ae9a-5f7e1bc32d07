/**
 * 数据模型导出文件
 * 统一导出所有数据模型和仓库类
 */

// 数据模型
const User = require('./User');
const Account = require('./Account');
const Position = require('./Position');

// 数据仓库
const UserRepository = require('./repositories/UserRepository');
const AccountRepository = require('./repositories/AccountRepository');
const PositionRepository = require('./repositories/PositionRepository');

// 集合配置
const userCollection = require('./collections/users');
const accountCollection = require('./collections/accounts');
const positionCollection = require('./collections/positions');

/**
 * 初始化所有数据库集合
 * @param {Object} db 数据库实例
 * @returns {Promise<boolean>} 初始化结果
 */
async function initializeAllCollections(db) {
  try {
    console.log('开始初始化数据库集合...');
    
    // 初始化用户集合
    const userResult = await userCollection.initializeCollection(db);
    if (!userResult) {
      throw new Error('用户集合初始化失败');
    }
    
    // 初始化账户集合
    const accountResult = await accountCollection.initializeCollection(db);
    if (!accountResult) {
      throw new Error('账户集合初始化失败');
    }
    
    // 初始化持仓集合
    const positionResult = await positionCollection.initializeCollection(db);
    if (!positionResult) {
      throw new Error('持仓集合初始化失败');
    }
    
    console.log('所有数据库集合初始化成功');
    return true;
  } catch (error) {
    console.error('数据库集合初始化失败:', error);
    return false;
  }
}

/**
 * 创建所有数据仓库实例
 * @param {Object} db 数据库实例
 * @returns {Object} 数据仓库实例集合
 */
function createRepositories(db) {
  return {
    userRepository: new UserRepository(db),
    accountRepository: new AccountRepository(db),
    positionRepository: new PositionRepository(db)
  };
}

module.exports = {
  // 数据模型
  User,
  Account,
  Position,
  
  // 数据仓库
  UserRepository,
  AccountRepository,
  PositionRepository,
  
  // 集合配置
  userCollection,
  accountCollection,
  positionCollection,
  
  // 工具函数
  initializeAllCollections,
  createRepositories
};