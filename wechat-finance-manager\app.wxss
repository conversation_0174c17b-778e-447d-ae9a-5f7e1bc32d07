/**app.wxss**/
@import "styles/common.wxss";

/* 全局样式覆盖 */
page {
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 导航栏样式 */
.navigation-bar {
  background-color: var(--background-primary);
  color: var(--text-primary);
}

/* 底部导航栏样式 */
.tab-bar {
  background-color: var(--background-primary);
  border-top: 1rpx solid var(--border-color);
}

/* 全局加载样式 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.global-loading-content {
  background-color: var(--background-primary);
  padding: 40rpx;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.global-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.global-loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}