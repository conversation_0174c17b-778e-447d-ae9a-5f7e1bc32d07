/**
 * 持仓模型单元测试
 */

const Position = require('../../models/Position');

describe('Position Model', () => {
  describe('构造函数', () => {
    test('应该创建默认持仓实例', () => {
      const position = new Position();
      
      expect(position._id).toBeNull();
      expect(position.accountId).toBe('');
      expect(position.userId).toBe('');
      expect(position.symbol).toBe('');
      expect(position.name).toBe('');
      expect(position.type).toBe('stock');
      expect(position.quantity).toBe(0);
      expect(position.costPrice).toBe(0);
      expect(position.currentPrice).toBe(0);
      expect(position.marketValue).toBe(0);
      expect(position.unrealizedPnL).toBe(0);
      expect(position.unrealizedPnLPercent).toBe(0);
      expect(position.dailyChange).toBe(0);
      expect(position.dailyChangePercent).toBe(0);
      expect(position.totalCost).toBe(0);
      expect(position.availableQuantity).toBe(0);
      expect(position.frozenQuantity).toBe(0);
      expect(position.dividendAmount).toBe(0);
      expect(position.isActive).toBe(true);
      expect(position.notes).toBe('');
      expect(position.createdAt).toBeInstanceOf(Date);
      expect(position.updatedAt).toBeInstanceOf(Date);
    });

    test('应该使用提供的数据创建持仓实例', () => {
      const positionData = {
        _id: 'test-position-id',
        accountId: 'test-account-id',
        userId: 'test-user-id',
        symbol: '000001',
        name: '平安银行',
        type: 'stock',
        quantity: 1000,
        costPrice: 15.50,
        currentPrice: 16.20,
        availableQuantity: 1000,
        frozenQuantity: 0,
        dividendAmount: 100,
        isActive: true,
        notes: '长期持有'
      };

      const position = new Position(positionData);
      
      expect(position._id).toBe('test-position-id');
      expect(position.accountId).toBe('test-account-id');
      expect(position.userId).toBe('test-user-id');
      expect(position.symbol).toBe('000001');
      expect(position.name).toBe('平安银行');
      expect(position.type).toBe('stock');
      expect(position.quantity).toBe(1000);
      expect(position.costPrice).toBe(15.50);
      expect(position.currentPrice).toBe(16.20);
      expect(position.availableQuantity).toBe(1000);
      expect(position.frozenQuantity).toBe(0);
      expect(position.dividendAmount).toBe(100);
      expect(position.isActive).toBe(true);
      expect(position.notes).toBe('长期持有');
    });
  });

  describe('数据验证', () => {
    test('应该验证有效的持仓数据', () => {
      const position = new Position({
        accountId: 'valid-account-id',
        userId: 'valid-user-id',
        symbol: '000001',
        name: '平安银行',
        type: 'stock',
        quantity: 1000,
        costPrice: 15.50,
        currentPrice: 16.20,
        availableQuantity: 1000
      });

      const validation = position.validate();
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('应该检测缺少必填字段', () => {
      const position = new Position({
        symbol: '000001'
      });

      const validation = position.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('账户ID是必填字段且必须为字符串');
      expect(validation.errors).toContain('用户ID是必填字段且必须为字符串');
    });

    test('应该检测无效的证券类型', () => {
      const position = new Position({
        accountId: 'test-account-id',
        userId: 'test-user-id',
        symbol: '000001',
        type: 'invalid-type'
      });

      const validation = position.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('证券类型只能是stock、fund或bond');
    });

    test('应该检测负数数量', () => {
      const position = new Position({
        accountId: 'test-account-id',
        userId: 'test-user-id',
        symbol: '000001',
        type: 'stock',
        quantity: -100
      });

      const validation = position.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('持仓数量必须为非负数');
    });

    test('应该检测负数价格', () => {
      const position = new Position({
        accountId: 'test-account-id',
        userId: 'test-user-id',
        symbol: '000001',
        type: 'stock',
        costPrice: -10
      });

      const validation = position.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('成本价必须为非负数');
    });

    test('应该检测数量逻辑错误', () => {
      const position = new Position({
        accountId: 'test-account-id',
        userId: 'test-user-id',
        symbol: '000001',
        type: 'stock',
        quantity: 1000,
        availableQuantity: 800,
        frozenQuantity: 300 // 800 + 300 > 1000
      });

      const validation = position.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('可用数量加冻结数量不能超过总持仓数量');
    });

    test('应该检测过长的备注', () => {
      const position = new Position({
        accountId: 'test-account-id',
        userId: 'test-user-id',
        symbol: '000001',
        type: 'stock',
        notes: 'a'.repeat(501) // 501个字符
      });

      const validation = position.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('备注长度不能超过500个字符');
    });
  });

  describe('证券代码验证', () => {
    test('应该验证有效的A股代码', () => {
      const position = new Position();
      
      expect(position.isValidSymbol('000001')).toBe(true);
      expect(position.isValidSymbol('600000')).toBe(true);
      expect(position.isValidSymbol('300001')).toBe(true);
    });

    test('应该验证有效的港股代码', () => {
      const position = new Position();
      
      expect(position.isValidSymbol('00700')).toBe(true);
      expect(position.isValidSymbol('09988')).toBe(true);
    });

    test('应该验证有效的美股代码', () => {
      const position = new Position();
      
      expect(position.isValidSymbol('AAPL')).toBe(true);
      expect(position.isValidSymbol('TSLA')).toBe(true);
      expect(position.isValidSymbol('MSFT')).toBe(true);
    });

    test('应该拒绝无效的证券代码', () => {
      const position = new Position();
      
      expect(position.isValidSymbol('123')).toBe(false);
      expect(position.isValidSymbol('********')).toBe(false);
      expect(position.isValidSymbol('ABC123')).toBe(false);
      expect(position.isValidSymbol('')).toBe(false);
    });
  });

  describe('数据库操作', () => {
    test('应该转换为数据库存储格式', () => {
      const position = new Position({
        accountId: 'test-account-id',
        userId: 'test-user-id',
        symbol: '000001',
        name: '平安银行',
        type: 'stock',
        quantity: 1000,
        costPrice: 15.50,
        currentPrice: 16.20
      });

      const dbData = position.toDatabase();
      
      expect(dbData.accountId).toBe('test-account-id');
      expect(dbData.userId).toBe('test-user-id');
      expect(dbData.symbol).toBe('000001');
      expect(dbData.name).toBe('平安银行');
      expect(dbData.type).toBe('stock');
      expect(dbData.quantity).toBe(1000);
      expect(dbData.costPrice).toBe(15.50);
      expect(dbData.currentPrice).toBe(16.20);
      expect(dbData.updatedAt).toBeInstanceOf(Date);
      expect(dbData.createdAt).toBeInstanceOf(Date);
    });

    test('应该从数据库数据创建持仓实例', () => {
      const dbData = {
        _id: 'db-position-id',
        accountId: 'db-account-id',
        userId: 'db-user-id',
        symbol: '600036',
        name: '招商银行',
        type: 'stock',
        quantity: 500,
        costPrice: 45.80,
        currentPrice: 47.20,
        marketValue: 23600,
        unrealizedPnL: 700,
        unrealizedPnLPercent: 3.06,
        availableQuantity: 500,
        frozenQuantity: 0,
        isActive: true,
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02')
      };

      const position = Position.fromDatabase(dbData);
      
      expect(position._id).toBe('db-position-id');
      expect(position.accountId).toBe('db-account-id');
      expect(position.userId).toBe('db-user-id');
      expect(position.symbol).toBe('600036');
      expect(position.name).toBe('招商银行');
      expect(position.type).toBe('stock');
      expect(position.quantity).toBe(500);
      expect(position.costPrice).toBe(45.80);
      expect(position.currentPrice).toBe(47.20);
      expect(position.marketValue).toBe(23600);
      expect(position.unrealizedPnL).toBe(700);
      expect(position.unrealizedPnLPercent).toBe(3.06);
    });
  });

  describe('持仓信息更新', () => {
    test('应该更新持仓信息', () => {
      const position = new Position({
        accountId: 'test-account-id',
        userId: 'test-user-id',
        symbol: '000001',
        quantity: 1000,
        costPrice: 15.50,
        currentPrice: 16.20
      });

      const originalUpdatedAt = position.updatedAt;

      setTimeout(() => {
        position.update({
          quantity: 1500,
          costPrice: 16.00,
          currentPrice: 16.50,
          notes: '加仓后'
        });

        expect(position.quantity).toBe(1500);
        expect(position.costPrice).toBe(16.00);
        expect(position.currentPrice).toBe(16.50);
        expect(position.notes).toBe('加仓后');
        expect(position.symbol).toBe('000001'); // 保持不变
        expect(position.updatedAt).not.toBe(originalUpdatedAt);
      }, 1);
    });
  });

  describe('数值计算', () => {
    test('应该正确计算持仓相关数值', () => {
      const position = new Position({
        quantity: 1000,
        costPrice: 15.50,
        currentPrice: 16.20
      });

      position.recalculate();

      expect(position.totalCost).toBe(15500); // 1000 * 15.50
      expect(position.marketValue).toBe(16200); // 1000 * 16.20
      expect(position.unrealizedPnL).toBe(700); // 16200 - 15500
      expect(position.unrealizedPnLPercent).toBeCloseTo(4.52, 2); // 700/15500 * 100
    });

    test('应该处理零成本的情况', () => {
      const position = new Position({
        quantity: 1000,
        costPrice: 0,
        currentPrice: 16.20
      });

      position.recalculate();

      expect(position.totalCost).toBe(0);
      expect(position.marketValue).toBe(16200);
      expect(position.unrealizedPnL).toBe(16200);
      expect(position.unrealizedPnLPercent).toBe(0); // 避免除零错误
    });
  });

  describe('价格更新', () => {
    test('应该更新价格信息', () => {
      const position = new Position({
        quantity: 1000,
        costPrice: 15.50,
        currentPrice: 16.00
      });

      position.updatePrice(16.50, 0.30, 1.85);

      expect(position.currentPrice).toBe(16.50);
      expect(position.dailyChange).toBe(0.30);
      expect(position.dailyChangePercent).toBe(1.85);
      expect(position.marketValue).toBe(16500); // 重新计算
      expect(position.priceUpdateTime).toBeInstanceOf(Date);
    });
  });

  describe('显示信息', () => {
    test('应该返回持仓显示信息', () => {
      const position = new Position({
        _id: 'display-position-id',
        symbol: '000001',
        name: '平安银行',
        type: 'stock',
        quantity: 1000,
        costPrice: 15.50,
        currentPrice: 16.20,
        marketValue: 16200,
        unrealizedPnL: 700,
        unrealizedPnLPercent: 4.52,
        availableQuantity: 1000,
        frozenQuantity: 0,
        isActive: true,
        notes: '长期持有'
      });

      const displayInfo = position.getDisplayInfo();
      
      expect(displayInfo._id).toBe('display-position-id');
      expect(displayInfo.symbol).toBe('000001');
      expect(displayInfo.name).toBe('平安银行');
      expect(displayInfo.type).toBe('stock');
      expect(displayInfo.typeName).toBe('股票');
      expect(displayInfo.quantity).toBe(1000);
      expect(displayInfo.costPrice).toBe(15.50);
      expect(displayInfo.currentPrice).toBe(16.20);
      expect(displayInfo.marketValue).toBe(16200);
      expect(displayInfo.unrealizedPnL).toBe(700);
      expect(displayInfo.unrealizedPnLPercent).toBe(4.52);
      expect(displayInfo.profitStatus).toBe('profit');
      expect(displayInfo.notes).toBe('长期持有');
      expect(displayInfo.accountId).toBeUndefined(); // 不应包含敏感信息
    });

    test('应该为空名称提供默认值', () => {
      const position = new Position({
        symbol: '000001'
      });

      const displayInfo = position.getDisplayInfo();
      
      expect(displayInfo.name).toBe('000001');
    });
  });

  describe('盈亏状态', () => {
    test('应该正确判断盈利状态', () => {
      const profitPosition = new Position({ unrealizedPnL: 100 });
      expect(profitPosition.getProfitStatus()).toBe('profit');

      const lossPosition = new Position({ unrealizedPnL: -100 });
      expect(lossPosition.getProfitStatus()).toBe('loss');

      const breakEvenPosition = new Position({ unrealizedPnL: 0 });
      expect(breakEvenPosition.getProfitStatus()).toBe('break-even');
    });
  });

  describe('持仓权重', () => {
    test('应该计算持仓权重', () => {
      const position = new Position({
        marketValue: 10000
      });

      const weight = position.getWeight(50000);
      
      expect(weight).toBe(20); // 10000/50000 * 100
    });

    test('应该处理零总价值的情况', () => {
      const position = new Position({
        marketValue: 10000
      });

      const weight = position.getWeight(0);
      
      expect(weight).toBe(0);
    });
  });

  describe('持仓操作', () => {
    test('应该检查是否可以卖出', () => {
      const position = new Position({
        availableQuantity: 1000
      });

      expect(position.canSell(500)).toBe(true);
      expect(position.canSell(1000)).toBe(true);
      expect(position.canSell(1500)).toBe(false);
    });

    test('应该冻结持仓', () => {
      const position = new Position({
        availableQuantity: 1000,
        frozenQuantity: 0
      });

      const result = position.freeze(300);
      
      expect(result).toBe(true);
      expect(position.availableQuantity).toBe(700);
      expect(position.frozenQuantity).toBe(300);
    });

    test('应该拒绝冻结超出可用数量', () => {
      const position = new Position({
        availableQuantity: 1000,
        frozenQuantity: 0
      });

      const result = position.freeze(1500);
      
      expect(result).toBe(false);
      expect(position.availableQuantity).toBe(1000);
      expect(position.frozenQuantity).toBe(0);
    });

    test('应该解冻持仓', () => {
      const position = new Position({
        availableQuantity: 700,
        frozenQuantity: 300
      });

      const result = position.unfreeze(200);
      
      expect(result).toBe(true);
      expect(position.availableQuantity).toBe(900);
      expect(position.frozenQuantity).toBe(100);
    });

    test('应该拒绝解冻超出冻结数量', () => {
      const position = new Position({
        availableQuantity: 700,
        frozenQuantity: 300
      });

      const result = position.unfreeze(500);
      
      expect(result).toBe(false);
      expect(position.availableQuantity).toBe(700);
      expect(position.frozenQuantity).toBe(300);
    });
  });

  describe('权限检查', () => {
    test('应该验证用户权限', () => {
      const position = new Position({
        userId: 'owner-user-id'
      });

      expect(position.checkPermission('owner-user-id')).toBe(true);
      expect(position.checkPermission('other-user-id')).toBe(false);
    });
  });
});