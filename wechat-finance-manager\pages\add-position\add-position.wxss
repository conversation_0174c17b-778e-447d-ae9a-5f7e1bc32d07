/* pages/add-position/add-position.wxss */
@import "../../styles/common.wxss";

.container {
  min-height: 100vh;
  background-color: var(--bg-color);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 24rpx;
  height: 24rpx;
  border-width: 2rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表单容器 */
.form-container {
  padding: 32rpx;
}

/* 表单组 */
.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.required {
  margin-left: 8rpx;
  font-size: 32rpx;
  color: var(--error-color);
}

.optional {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 输入框容器 */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  height: 88rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  background-color: var(--card-bg);
  border: 2rpx solid var(--border-color);
  border-radius: 12rpx;
  color: var(--text-primary);
}

.form-input.error {
  border-color: var(--error-color);
}

.form-input:focus {
  border-color: var(--primary-color);
}

.input-unit {
  position: absolute;
  right: 24rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  pointer-events: none;
}

.input-loading {
  position: absolute;
  right: 24rpx;
}

/* 选择器 */
.form-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background-color: var(--card-bg);
  border: 2rpx solid var(--border-color);
  border-radius: 12rpx;
}

.picker-display.error {
  border-color: var(--error-color);
}

.picker-text {
  font-size: 32rpx;
  color: var(--text-primary);
}

.picker-arrow {
  font-size: 28rpx;
  color: var(--text-secondary);
  transform: rotate(90deg);
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  font-size: 32rpx;
  background-color: var(--card-bg);
  border: 2rpx solid var(--border-color);
  border-radius: 12rpx;
  color: var(--text-primary);
  box-sizing: border-box;
}

.form-textarea.error {
  border-color: var(--error-color);
}

.textarea-counter {
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
}

.counter-text {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 错误和成功提示 */
.error-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: var(--error-color);
}

.success-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: var(--success-color);
}

/* 预览容器 */
.preview-container {
  margin: 40rpx 0;
  padding: 32rpx;
  background-color: var(--card-bg);
  border-radius: 16rpx;
  border: 2rpx solid var(--border-color);
}

.preview-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 20rpx;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.preview-value {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
}

/* 按钮容器 */
.button-container {
  display: flex;
  gap: 24rpx;
  margin-top: 60rpx;
  padding-bottom: 40rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:disabled {
  background-color: var(--disabled-color);
  color: var(--disabled-text-color);
}

.btn-secondary {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.btn-secondary:disabled {
  background-color: var(--disabled-bg-color);
  color: var(--disabled-text-color);
  border-color: var(--disabled-color);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .form-input,
  .picker-display,
  .form-textarea {
    background-color: var(--card-bg-dark);
    border-color: var(--border-color-dark);
    color: var(--text-primary-dark);
  }
  
  .preview-container {
    background-color: var(--card-bg-dark);
    border-color: var(--border-color-dark);
  }
  
  .btn-secondary {
    background-color: var(--card-bg-dark);
    color: var(--text-primary-dark);
    border-color: var(--border-color-dark);
  }
}