<!--pages/import-positions/import-positions.wxml-->
<view class="container">
  <!-- 文件选择步骤 -->
  <view wx:if="{{importStep === 'select'}}" class="step-container">
    <view class="step-header">
      <view class="step-title">选择导入文件</view>
      <view class="step-subtitle">支持CSV和Excel格式文件</view>
    </view>

    <!-- 模板说明 -->
    <view class="template-section">
      <view class="section-title">
        <text class="title-text">文件格式要求</text>
        <button class="btn-link" bindtap="downloadTemplate">下载模板</button>
      </view>
      
      <view class="template-table">
        <view class="table-header">
          <text class="col-name">列名</text>
          <text class="col-required">必填</text>
          <text class="col-example">示例</text>
        </view>
        <view wx:for="{{templateColumns}}" wx:key="key" class="table-row">
          <text class="col-name">{{item.name}}</text>
          <text class="col-required {{item.required ? 'required' : 'optional'}}">
            {{item.required ? '是' : '否'}}
          </text>
          <text class="col-example">{{item.example}}</text>
        </view>
      </view>
    </view>

    <!-- 文件选择 -->
    <view class="file-select-section">
      <button class="btn btn-primary btn-large" bindtap="selectFile">
        选择文件
      </button>
      <view class="file-tips">
        <text class="tip-text">• 文件大小不超过5MB</text>
        <text class="tip-text">• 支持.csv、.xlsx、.xls格式</text>
        <text class="tip-text">• 请确保数据格式正确</text>
      </view>
    </view>
  </view>

  <!-- 数据预览步骤 -->
  <view wx:if="{{importStep === 'preview'}}" class="step-container">
    <view class="step-header">
      <view class="step-title">数据预览</view>
      <view class="step-subtitle">
        文件：{{fileInfo.name}} ({{formatFileSize(fileInfo.size)}})
      </view>
    </view>

    <!-- 数据统计 -->
    <view class="data-summary">
      <view class="summary-item">
        <text class="summary-label">总计</text>
        <text class="summary-value">{{parsedData.length}}</text>
      </view>
      <view class="summary-item success">
        <text class="summary-label">有效</text>
        <text class="summary-value">{{validData.length}}</text>
      </view>
      <view class="summary-item error">
        <text class="summary-label">无效</text>
        <text class="summary-value">{{invalidData.length}}</text>
      </view>
    </view>

    <!-- 有效数据预览 -->
    <view wx:if="{{validData.length > 0}}" class="data-preview">
      <view class="preview-title">有效数据 (前5条)</view>
      <scroll-view class="preview-table" scroll-x>
        <view class="table-header">
          <text class="col">证券代码</text>
          <text class="col">证券名称</text>
          <text class="col">类型</text>
          <text class="col">数量</text>
          <text class="col">成本价</text>
        </view>
        <view wx:for="{{validData.slice(0, 5)}}" wx:key="index" class="table-row">
          <text class="col">{{item.symbol}}</text>
          <text class="col">{{item.name || '-'}}</text>
          <text class="col">{{item.type}}</text>
          <text class="col">{{item.quantity}}</text>
          <text class="col">¥{{item.costPrice}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 无效数据预览 -->
    <view wx:if="{{invalidData.length > 0}}" class="error-preview">
      <view class="preview-title error">无效数据</view>
      <view wx:for="{{invalidData.slice(0, 5)}}" wx:key="index" class="error-item">
        <view class="error-header">
          <text class="error-row">第{{item.index}}行</text>
          <text class="error-symbol">{{item.symbol || '未知'}}</text>
        </view>
        <view class="error-messages">
          <text wx:for="{{item.errors}}" wx:for-item="error" wx:key="*this" class="error-message">
            • {{error}}
          </text>
        </view>
      </view>
      <view wx:if="{{invalidData.length > 5}}" class="more-errors">
        还有{{invalidData.length - 5}}条无效数据...
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-container">
      <button class="btn btn-secondary" bindtap="reselectFile">
        重新选择
      </button>
      <button 
        class="btn btn-primary" 
        bindtap="startImport"
        disabled="{{validData.length === 0}}"
      >
        开始导入 ({{validData.length}})
      </button>
    </view>
  </view>

  <!-- 导入进行中 -->
  <view wx:if="{{importStep === 'importing'}}" class="step-container">
    <view class="step-header">
      <view class="step-title">正在导入</view>
      <view class="step-subtitle">请稍候，正在处理数据...</view>
    </view>

    <!-- 进度显示 -->
    <view class="import-progress">
      <view class="progress-circle">
        <text class="progress-text">{{getImportProgress()}}%</text>
      </view>
      
      <view class="progress-details">
        <view class="progress-item">
          <text class="progress-label">总计：</text>
          <text class="progress-value">{{importProgress.total}}</text>
        </view>
        <view class="progress-item">
          <text class="progress-label">已处理：</text>
          <text class="progress-value">{{importProgress.current}}</text>
        </view>
        <view class="progress-item success">
          <text class="progress-label">成功：</text>
          <text class="progress-value">{{importProgress.success}}</text>
        </view>
        <view class="progress-item error">
          <text class="progress-label">失败：</text>
          <text class="progress-value">{{importProgress.failed}}</text>
        </view>
      </view>
    </view>

    <!-- 实时错误显示 -->
    <view wx:if="{{importProgress.errors.length > 0}}" class="import-errors">
      <view class="errors-title">导入错误</view>
      <scroll-view class="errors-list" scroll-y style="height: 200rpx;">
        <view wx:for="{{importProgress.errors}}" wx:key="row" class="error-item">
          <text class="error-symbol">{{item.symbol}}</text>
          <text class="error-message">{{item.error}}</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 导入完成 -->
  <view wx:if="{{importStep === 'complete'}}" class="step-container">
    <view class="step-header">
      <view class="step-title">导入完成</view>
      <view class="step-subtitle">数据导入已完成</view>
    </view>

    <!-- 结果统计 -->
    <view class="result-summary">
      <view class="result-item success">
        <view class="result-number">{{importProgress.success}}</view>
        <view class="result-label">成功导入</view>
      </view>
      <view class="result-item error">
        <view class="result-number">{{importProgress.failed}}</view>
        <view class="result-label">导入失败</view>
      </view>
    </view>

    <!-- 错误详情 -->
    <view wx:if="{{importProgress.errors.length > 0}}" class="final-errors">
      <view class="errors-title">失败详情</view>
      <scroll-view class="errors-list" scroll-y style="height: 300rpx;">
        <view wx:for="{{importProgress.errors}}" wx:key="row" class="error-item">
          <view class="error-header">
            <text class="error-row">第{{item.row}}行</text>
            <text class="error-symbol">{{item.symbol}}</text>
          </view>
          <text class="error-message">{{item.error}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 完成按钮 -->
    <view class="button-container">
      <button class="btn btn-primary btn-block" bindtap="backToAccount">
        返回账户详情
      </button>
    </view>
  </view>

  <!-- 解析中遮罩 -->
  <view wx:if="{{parsing}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在解析文件...</text>
    </view>
  </view>
</view>