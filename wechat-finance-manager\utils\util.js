// utils/util.js

/**
 * 格式化时间
 */
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

/**
 * 格式化数字（补零）
 */
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

/**
 * 格式化金额
 */
const formatMoney = (amount, decimals = 2) => {
  if (amount === null || amount === undefined) return '0.00'
  
  const num = parseFloat(amount)
  if (isNaN(num)) return '0.00'
  
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化百分比
 */
const formatPercent = (value, decimals = 2) => {
  if (value === null || value === undefined) return '0.00%'
  
  const num = parseFloat(value)
  if (isNaN(num)) return '0.00%'
  
  return `${num.toFixed(decimals)}%`
}

/**
 * 格式化涨跌幅
 */
const formatChange = (change, percent) => {
  const changeStr = change >= 0 ? `+${formatMoney(change)}` : formatMoney(change)
  const percentStr = percent >= 0 ? `+${formatPercent(percent)}` : formatPercent(percent)
  return `${changeStr} (${percentStr})`
}

/**
 * 验证证券代码
 */
const validateStockCode = (code) => {
  if (!code) return false
  // A股代码格式：6位数字
  return /^[0-9]{6}$/.test(code)
}

/**
 * 验证基金代码
 */
const validateFundCode = (code) => {
  if (!code) return false
  // 基金代码格式：6位数字
  return /^[0-9]{6}$/.test(code)
}

/**
 * 验证账户号码
 */
const validateAccountNumber = (number) => {
  if (!number) return false
  // 账户号码：10-20位数字
  return /^\d{10,20}$/.test(number)
}

/**
 * 防抖函数
 */
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 生成唯一ID
 */
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 获取文件扩展名
 */
const getFileExtension = (filename) => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

/**
 * 计算两个日期之间的天数
 */
const daysBetween = (date1, date2) => {
  const oneDay = 24 * 60 * 60 * 1000
  const firstDate = new Date(date1)
  const secondDate = new Date(date2)
  return Math.round(Math.abs((firstDate - secondDate) / oneDay))
}

/**
 * 显示加载提示
 */
const showLoading = (title = '加载中...') => {
  wx.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
const hideLoading = () => {
  wx.hideLoading()
}

/**
 * 显示成功提示
 */
const showSuccess = (title = '操作成功') => {
  wx.showToast({
    title,
    icon: 'success',
    duration: 2000
  })
}

/**
 * 显示错误提示
 */
const showError = (title = '操作失败') => {
  wx.showToast({
    title,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 显示确认对话框
 */
const showConfirm = (content, title = '提示') => {
  return new Promise((resolve) => {
    wx.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm)
      }
    })
  })
}

module.exports = {
  formatTime,
  formatNumber,
  formatMoney,
  formatPercent,
  formatChange,
  validateStockCode,
  validateFundCode,
  validateAccountNumber,
  debounce,
  throttle,
  deepClone,
  generateId,
  getFileExtension,
  daysBetween,
  showLoading,
  hideLoading,
  showSuccess,
  showError,
  showConfirm
}