// components/theme-switcher/theme-switcher.js
const ThemeService = require('../../services/ThemeService')

Component({
  properties: {
    showQuickSwitch: {
      type: Boolean,
      value: false
    },
    theme: {
      type: String,
      value: 'light'
    }
  },

  data: {
    currentTheme: 'light',
    themes: [],
    switching: false
  },

  lifetimes: {
    attached() {
      this.initThemes()
      this.setupThemeListener()
    },

    detached() {
      this.removeThemeListener()
    }
  },

  methods: {
    // 初始化主题选项
    initThemes() {
      const themes = ThemeService.getAvailableThemes()
      const currentTheme = ThemeService.getCurrentTheme()
      
      this.setData({
        themes: themes,
        currentTheme: currentTheme
      })
    },

    // 设置主题监听器
    setupThemeListener() {
      this.themeChangeHandler = (theme) => {
        this.setData({
          currentTheme: theme
        })
      }
      
      ThemeService.addThemeListener(this.themeChangeHandler)
    },

    // 移除主题监听器
    removeThemeListener() {
      if (this.themeChangeHandler) {
        ThemeService.removeThemeListener(this.themeChangeHandler)
      }
    },

    // 切换主题
    switchTheme(e) {
      const { theme } = e.currentTarget.dataset
      
      if (theme === this.data.currentTheme || this.data.switching) {
        return
      }

      this.setData({ switching: true })

      // 添加切换动画
      setTimeout(() => {
        const newTheme = ThemeService.switchTheme(theme)
        
        this.setData({
          currentTheme: newTheme,
          switching: false
        })

        // 触发主题切换事件
        this.triggerEvent('themechange', {
          theme: newTheme,
          config: ThemeService.getThemeConfig(newTheme)
        })

        // 显示切换成功提示
        wx.showToast({
          title: `已切换到${ThemeService.getThemeDisplayName(newTheme)}`,
          icon: 'success',
          duration: 1500
        })
      }, 100)
    },

    // 快速切换（在浅色和深色之间）
    quickSwitch() {
      if (this.data.switching) {
        return
      }

      this.setData({ switching: true })

      const newTheme = ThemeService.switchTheme()
      
      this.setData({
        currentTheme: newTheme,
        switching: false
      })

      // 触发主题切换事件
      this.triggerEvent('themechange', {
        theme: newTheme,
        config: ThemeService.getThemeConfig(newTheme)
      })

      // 触觉反馈
      wx.vibrateShort({
        type: 'light'
      })
    },

    // 获取当前主题配置
    getCurrentThemeConfig() {
      return ThemeService.getThemeConfig(this.data.currentTheme)
    }
  }
})