/* pages/settings/settings.wxss */
.container {
  padding: 20rpx;
  background-color: var(--background-secondary);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 用户信息区域 */
.user-section {
  margin-bottom: 40rpx;
}

.user-card {
  background: var(--background-primary);
  border-radius: var(--border-radius);
  padding: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-light);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 设置区域 */
.settings-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 24rpx;
  padding-left: 8rpx;
}

/* 主题设置卡片 */
.theme-setting-card {
  background: var(--background-primary);
  border-radius: var(--border-radius);
  padding: 30rpx;
  box-shadow: var(--shadow-light);
}

.setting-header {
  margin-bottom: 30rpx;
}

.setting-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.setting-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 设置列表 */
.settings-list {
  background: var(--background-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.setting-item {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:active {
  background-color: var(--background-secondary);
}

.setting-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 48rpx;
  text-align: center;
}

.setting-info {
  flex: 1;
}

.setting-label {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 4rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.setting-arrow {
  font-size: 28rpx;
  color: var(--text-tertiary);
  margin-left: 16rpx;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}