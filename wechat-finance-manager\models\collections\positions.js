/**
 * 持仓集合配置
 * 定义持仓集合的数据库结构、索引和权限规则
 */

const COLLECTION_NAME = 'positions';

/**
 * 持仓集合的数据库索引配置
 */
const INDEXES = [
  {
    // 账户ID索引，用于快速查找账户的所有持仓
    keys: { accountId: 1 },
    options: { 
      name: 'account_id_index'
    }
  },
  {
    // 用户ID索引，用于快速查找用户的所有持仓
    keys: { userId: 1 },
    options: { 
      name: 'user_id_index'
    }
  },
  {
    // 证券代码索引，用于按证券查找持仓
    keys: { symbol: 1 },
    options: { 
      name: 'symbol_index'
    }
  },
  {
    // 证券类型索引，用于按类型筛选持仓
    keys: { type: 1 },
    options: { 
      name: 'type_index'
    }
  },
  {
    // 激活状态索引，用于筛选活跃持仓
    keys: { isActive: 1 },
    options: { 
      name: 'is_active_index'
    }
  },
  {
    // 账户+证券代码唯一索引，防止重复持仓
    keys: { accountId: 1, symbol: 1 },
    options: { 
      unique: true,
      name: 'account_symbol_unique'
    }
  },
  {
    // 创建时间索引，用于按时间排序
    keys: { createdAt: -1 },
    options: { 
      name: 'created_at_desc'
    }
  },
  {
    // 更新时间索引，用于查找最近更新的持仓
    keys: { updatedAt: -1 },
    options: { 
      name: 'updated_at_desc'
    }
  },
  {
    // 价格更新时间索引，用于价格数据管理
    keys: { priceUpdateTime: -1 },
    options: { 
      name: 'price_update_time_desc'
    }
  },
  {
    // 复合索引：用户ID + 证券类型 + 激活状态
    keys: { userId: 1, type: 1, isActive: 1 },
    options: { 
      name: 'user_type_active_index'
    }
  },
  {
    // 复合索引：账户ID + 激活状态 + 市值（用于排序）
    keys: { accountId: 1, isActive: 1, marketValue: -1 },
    options: { 
      name: 'account_active_value_index'
    }
  }
];

/**
 * 数据库权限规则
 * 确保用户只能访问自己的持仓数据
 */
const PERMISSION_RULES = {
  read: "auth.openid == resource.data.userId",
  write: "auth.openid == resource.data.userId",
  create: "auth.openid == resource.data.userId",
  delete: "auth.openid == resource.data.userId"
};

/**
 * 数据验证规则
 */
const VALIDATION_SCHEMA = {
  required: ['accountId', 'userId', 'symbol', 'type'],
  properties: {
    accountId: {
      type: 'string',
      minLength: 1
    },
    userId: {
      type: 'string',
      minLength: 1
    },
    symbol: {
      type: 'string',
      minLength: 1,
      maxLength: 20
    },
    name: {
      type: 'string',
      maxLength: 100
    },
    type: {
      type: 'string',
      enum: ['stock', 'fund', 'bond']
    },
    quantity: {
      type: 'number',
      minimum: 0
    },
    costPrice: {
      type: 'number',
      minimum: 0
    },
    currentPrice: {
      type: 'number',
      minimum: 0
    },
    marketValue: {
      type: 'number',
      minimum: 0
    },
    unrealizedPnL: {
      type: 'number'
    },
    unrealizedPnLPercent: {
      type: 'number'
    },
    dailyChange: {
      type: 'number'
    },
    dailyChangePercent: {
      type: 'number'
    },
    totalCost: {
      type: 'number',
      minimum: 0
    },
    availableQuantity: {
      type: 'number',
      minimum: 0
    },
    frozenQuantity: {
      type: 'number',
      minimum: 0
    },
    dividendAmount: {
      type: 'number',
      minimum: 0
    },
    lastTradeDate: {
      type: 'date'
    },
    priceUpdateTime: {
      type: 'date'
    },
    isActive: {
      type: 'boolean'
    },
    notes: {
      type: 'string',
      maxLength: 500
    },
    createdAt: { type: 'date' },
    updatedAt: { type: 'date' }
  }
};

/**
 * 证券类型配置
 */
const SECURITY_TYPES = {
  stock: {
    name: '股票',
    codePattern: /^[0-9]{6}$/,
    description: 'A股股票'
  },
  fund: {
    name: '基金',
    codePattern: /^[0-9]{6}$/,
    description: '公募基金'
  },
  bond: {
    name: '债券',
    codePattern: /^[0-9]{6}$/,
    description: '债券'
  }
};

/**
 * 市场配置
 */
const MARKETS = {
  SH: {
    name: '上海证券交易所',
    prefix: ['60', '68', '51'],
    description: '沪市'
  },
  SZ: {
    name: '深圳证券交易所',
    prefix: ['00', '30', '15'],
    description: '深市'
  },
  BJ: {
    name: '北京证券交易所',
    prefix: ['43', '83', '87'],
    description: '北交所'
  }
};

/**
 * 获取证券所属市场
 * @param {string} symbol 证券代码
 * @returns {string} 市场代码
 */
function getMarket(symbol) {
  if (!symbol || symbol.length !== 6) {
    return 'UNKNOWN';
  }

  const prefix = symbol.substring(0, 2);
  
  for (const [marketCode, market] of Object.entries(MARKETS)) {
    if (market.prefix.includes(prefix)) {
      return marketCode;
    }
  }
  
  return 'UNKNOWN';
}

/**
 * 验证证券代码格式
 * @param {string} symbol 证券代码
 * @param {string} type 证券类型
 * @returns {boolean} 是否有效
 */
function validateSymbol(symbol, type) {
  if (!symbol || !type) {
    return false;
  }

  const securityType = SECURITY_TYPES[type];
  if (!securityType) {
    return false;
  }

  return securityType.codePattern.test(symbol);
}

/**
 * 初始化持仓集合
 * 创建索引和设置权限规则
 */
async function initializeCollection(db) {
  try {
    const collection = db.collection(COLLECTION_NAME);
    
    // 创建索引
    for (const index of INDEXES) {
      await collection.createIndex(index.keys, index.options);
      console.log(`Created index: ${index.options.name}`);
    }
    
    console.log(`Position collection initialized successfully`);
    return true;
  } catch (error) {
    console.error('Failed to initialize position collection:', error);
    return false;
  }
}

module.exports = {
  COLLECTION_NAME,
  INDEXES,
  PERMISSION_RULES,
  VALIDATION_SCHEMA,
  SECURITY_TYPES,
  MARKETS,
  getMarket,
  validateSymbol,
  initializeCollection
};