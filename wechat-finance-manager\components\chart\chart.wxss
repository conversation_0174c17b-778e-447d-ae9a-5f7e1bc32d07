/* components/chart/chart.wxss */
.chart-container {
  width: 100%;
  position: relative;
  background-color: var(--background-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-canvas {
  width: 100%;
  display: block;
  border-radius: var(--border-radius);
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: var(--text-secondary);
  background-color: var(--background-primary);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: var(--text-tertiary);
  background-color: var(--background-primary);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .chart-container {
    margin: 0 20rpx;
  }
}

/* 深色模式适配 */
.chart-container[data-theme="dark"] {
  background-color: var(--background-secondary);
  box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.1);
}

.chart-container[data-theme="dark"] .loading-text,
.chart-container[data-theme="dark"] .empty-text {
  color: var(--text-primary);
}

/* 动画效果 */
.chart-container {
  transition: all 0.3s ease;
}

.chart-canvas {
  transition: opacity 0.3s ease;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chart-container {
  animation: fadeIn 0.5s ease-out;
}