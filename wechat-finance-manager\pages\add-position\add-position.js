// pages/add-position/add-position.js
import dataService from '../../services/DataService.js'
import Position from '../../models/Position.js'
import { validateSymbol, getMarket, SECURITY_TYPES } from '../../models/collections/positions.js'

Page({
  data: {
    // 页面模式：add(添加) 或 edit(编辑)
    mode: 'add',
    positionId: '',
    accountId: '',
    
    // 表单数据
    formData: {
      symbol: '',
      name: '',
      type: 'stock',
      quantity: '',
      costPrice: '',
      notes: ''
    },
    
    // 表单验证错误
    errors: {},
    
    // 证券类型选项
    securityTypes: [
      { value: 'stock', label: '股票' },
      { value: 'fund', label: '基金' },
      { value: 'bond', label: '债券' }
    ],
    
    // 证券信息
    securityInfo: {
      market: '',
      marketName: '',
      isValid: false
    },
    
    // 页面状态
    loading: false,
    submitting: false,
    symbolValidating: false
  },

  onLoad(options) {
    const { accountId, mode = 'add', id } = options
    
    if (!accountId) {
      wx.showToast({
        title: '账户ID不能为空',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({
      accountId,
      mode,
      positionId: id || ''
    })

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: mode === 'edit' ? '编辑持仓' : '添加持仓'
    })

    // 如果是编辑模式，加载持仓数据
    if (mode === 'edit' && id) {
      this.loadPositionData(id)
    }
  },

  // 加载持仓数据（编辑模式）
  async loadPositionData(positionId) {
    this.setData({ loading: true })

    try {
      const positions = await dataService.getPositions(this.data.accountId)
      if (positions.success) {
        const position = positions.data.find(p => p._id === positionId)
        if (position) {
          this.setData({
            formData: {
              symbol: position.symbol,
              name: position.name || '',
              type: position.type,
              quantity: position.quantity.toString(),
              costPrice: position.costPrice.toString(),
              notes: position.notes || ''
            }
          })
          // 验证证券代码
          this.validateSymbol(position.symbol)
        } else {
          wx.showToast({
            title: '持仓不存在',
            icon: 'error'
          })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      }
    } catch (error) {
      console.error('加载持仓数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }

    this.setData({ loading: false })
  },

  // 输入框变化处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除该字段的错误信息
    })

    // 证券代码变化时进行验证
    if (field === 'symbol') {
      this.debounceValidateSymbol(value)
    }
  },

  // 证券类型选择
  onTypeChange(e) {
    const type = e.detail.value
    const typeOption = this.data.securityTypes[type]
    
    this.setData({
      'formData.type': typeOption.value,
      'errors.type': ''
    })

    // 重新验证证券代码
    if (this.data.formData.symbol) {
      this.validateSymbol(this.data.formData.symbol)
    }
  },

  // 防抖验证证券代码
  debounceValidateSymbol(symbol) {
    if (this.validateTimer) {
      clearTimeout(this.validateTimer)
    }
    
    this.validateTimer = setTimeout(() => {
      this.validateSymbol(symbol)
    }, 500)
  },

  // 验证证券代码
  async validateSymbol(symbol) {
    if (!symbol) {
      this.setData({
        securityInfo: {
          market: '',
          marketName: '',
          isValid: false
        }
      })
      return
    }

    this.setData({ symbolValidating: true })

    try {
      // 基础格式验证
      const isValidFormat = validateSymbol(symbol, this.data.formData.type)
      
      if (!isValidFormat) {
        this.setData({
          'errors.symbol': '证券代码格式不正确',
          securityInfo: {
            market: '',
            marketName: '',
            isValid: false
          }
        })
        return
      }

      // 获取市场信息
      const market = getMarket(symbol)
      const marketNames = {
        'SH': '上海证券交易所',
        'SZ': '深圳证券交易所',
        'BJ': '北京证券交易所',
        'UNKNOWN': '未知市场'
      }

      // 检查是否已存在该持仓（仅在添加模式或编辑时代码发生变化）
      if (this.data.mode === 'add' || 
          (this.data.mode === 'edit' && symbol !== this.data.originalSymbol)) {
        const existingPositions = await dataService.getPositions(this.data.accountId)
        if (existingPositions.success) {
          const duplicate = existingPositions.data.find(p => 
            p.symbol === symbol && p._id !== this.data.positionId
          )
          if (duplicate) {
            this.setData({
              'errors.symbol': '该账户已存在此证券的持仓'
            })
            return
          }
        }
      }

      this.setData({
        'errors.symbol': '',
        securityInfo: {
          market,
          marketName: marketNames[market] || '未知市场',
          isValid: market !== 'UNKNOWN'
        }
      })

    } catch (error) {
      console.error('验证证券代码失败:', error)
      this.setData({
        'errors.symbol': '验证证券代码时发生错误'
      })
    } finally {
      this.setData({ symbolValidating: false })
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data
    const errors = {}

    // 验证证券代码
    if (!formData.symbol) {
      errors.symbol = '请输入证券代码'
    } else if (!validateSymbol(formData.symbol, formData.type)) {
      errors.symbol = '证券代码格式不正确'
    }

    // 验证证券名称（可选）
    if (formData.name && formData.name.length > 100) {
      errors.name = '证券名称不能超过100个字符'
    }

    // 验证持仓数量
    if (!formData.quantity) {
      errors.quantity = '请输入持仓数量'
    } else {
      const quantity = parseFloat(formData.quantity)
      if (isNaN(quantity) || quantity <= 0) {
        errors.quantity = '持仓数量必须大于0'
      } else if (quantity > 999999999) {
        errors.quantity = '持仓数量不能超过999,999,999'
      }
    }

    // 验证成本价
    if (!formData.costPrice) {
      errors.costPrice = '请输入成本价'
    } else {
      const costPrice = parseFloat(formData.costPrice)
      if (isNaN(costPrice) || costPrice <= 0) {
        errors.costPrice = '成本价必须大于0'
      } else if (costPrice > 999999) {
        errors.costPrice = '成本价不能超过999,999'
      }
    }

    // 验证备注
    if (formData.notes && formData.notes.length > 500) {
      errors.notes = '备注不能超过500个字符'
    }

    this.setData({ errors })
    return Object.keys(errors).length === 0
  },

  // 提交表单
  async submitForm() {
    if (this.data.submitting) return

    // 表单验证
    if (!this.validateForm()) {
      wx.showToast({
        title: '请检查输入信息',
        icon: 'error'
      })
      return
    }

    this.setData({ submitting: true })

    try {
      const { formData, accountId, mode, positionId } = this.data
      
      // 构建持仓数据
      const positionData = {
        accountId,
        symbol: formData.symbol.toUpperCase(),
        name: formData.name || formData.symbol,
        type: formData.type,
        quantity: parseFloat(formData.quantity),
        costPrice: parseFloat(formData.costPrice),
        notes: formData.notes || ''
      }

      let result
      if (mode === 'edit') {
        // 编辑模式
        result = await dataService.updatePosition(positionId, positionData)
      } else {
        // 添加模式
        result = await dataService.addPosition(positionData)
      }

      if (result.success) {
        wx.showToast({
          title: mode === 'edit' ? '更新成功' : '添加成功',
          icon: 'success'
        })
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.error?.message || '操作失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('提交表单失败:', error)
      wx.showToast({
        title: '操作异常',
        icon: 'error'
      })
    }

    this.setData({ submitting: false })
  },

  // 重置表单
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置表单吗？所有输入的信息将被清空。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            formData: {
              symbol: '',
              name: '',
              type: 'stock',
              quantity: '',
              costPrice: '',
              notes: ''
            },
            errors: {},
            securityInfo: {
              market: '',
              marketName: '',
              isValid: false
            }
          })
          wx.showToast({
            title: '表单已重置',
            icon: 'success'
          })
        }
      }
    })
  },

  // 获取证券类型显示名称
  getSecurityTypeName(type) {
    const typeMap = {
      stock: '股票',
      fund: '基金',
      bond: '债券'
    }
    return typeMap[type] || '未知类型'
  },

  // 格式化数字输入
  formatNumberInput(e) {
    const { field } = e.currentTarget.dataset
    let { value } = e.detail
    
    // 移除非数字和小数点的字符
    value = value.replace(/[^\d.]/g, '')
    
    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
      value = parts[0] + '.' + parts.slice(1).join('')
    }
    
    // 限制小数位数
    if (parts.length === 2) {
      if (field === 'quantity') {
        // 数量最多3位小数
        parts[1] = parts[1].substring(0, 3)
      } else if (field === 'costPrice') {
        // 价格最多4位小数
        parts[1] = parts[1].substring(0, 4)
      }
      value = parts.join('.')
    }
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: ''
    })
    
    return { value }
  },

  // 页面卸载时清理定时器
  onUnload() {
    if (this.validateTimer) {
      clearTimeout(this.validateTimer)
    }
  }
})