# 微信小程序理财管家

一个采用iOS风格扁平化设计的个人资产管理工具，使用微信云开发模式构建。

## 项目概述

本应用分三个阶段实现，涵盖证券资产统计、交易记录管理和基金账户管理功能，为用户提供全面的投资理财数据管理和分析服务。

## 技术栈

- **前端**: 微信小程序原生框架
- **后端**: 微信云开发
- **数据库**: 微信云数据库
- **存储**: 微信云存储
- **云函数**: Node.js

## 项目结构

```
wechat-finance-manager/
├── pages/                  # 页面目录
│   ├── index/              # 首页
│   ├── accounts/           # 账户管理
│   ├── transactions/       # 交易记录
│   ├── funds/              # 基金管理
│   └── settings/           # 设置页面
├── components/             # 组件目录
│   └── chart/              # 图表组件
├── services/               # 服务层
│   ├── DataService.js      # 数据服务
│   ├── AuthService.js      # 认证服务
│   └── PriceService.js     # 价格服务
├── utils/                  # 工具函数
│   ├── util.js             # 通用工具
│   └── calculator.js       # 计算器工具
├── styles/                 # 样式文件
│   └── common.wxss         # 通用样式
├── cloudfunctions/         # 云函数
│   ├── login/              # 登录函数
│   └── getPrices/          # 价格获取函数
├── images/                 # 图片资源
├── app.js                  # 应用入口
├── app.json                # 应用配置
├── app.wxss                # 全局样式
├── project.config.json     # 项目配置
└── sitemap.json           # 站点地图
```

## 功能特性

### 第一阶段：证券资产统计
- ✅ 证券账户管理
- ✅ 持仓数据管理
- ✅ 资产价值计算
- ✅ 资产变化图表

### 第二阶段：交易记录管理
- ⏳ 券商收费配置
- ⏳ 交易记录管理
- ⏳ 盈亏计算与报表

### 第三阶段：基金账户管理
- ⏳ 基金数据集成
- ⏳ 基金账户管理
- ⏳ 基金收益分析

## 开发环境设置

### 前置要求
- 微信开发者工具
- Node.js 14+
- 微信小程序开发账号

### 安装步骤

1. 克隆项目到本地
2. 使用微信开发者工具打开项目
3. 配置云开发环境
4. 部署云函数
5. 初始化云数据库

### 云开发配置

1. 在微信开发者工具中开通云开发
2. 创建云环境（建议命名为 `finance-manager-cloud`）
3. 部署云函数：
   ```bash
   # 在云开发控制台中上传并部署以下云函数
   - login
   - getPrices
   ```

### 数据库集合

需要创建以下数据库集合：
- `users` - 用户信息
- `accounts` - 账户信息
- `positions` - 持仓信息
- `transactions` - 交易记录
- `prices` - 价格数据
- `configs` - 配置信息

## 代码规范

项目使用 ESLint 进行代码规范检查，主要规则：
- 使用 2 空格缩进
- 使用单引号
- 不使用分号
- 驼峰命名法
- 最大行长度 120 字符

## 设计规范

- 遵循 iOS 设计规范
- 扁平化设计风格
- 支持深色/浅色模式
- 响应式布局适配

## 部署说明

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 提交审核
4. 审核通过后发布

## 许可证

本项目仅供学习和参考使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮箱：[<EMAIL>]

---

**注意**: 本项目目前处于开发阶段，部分功能尚未完全实现。