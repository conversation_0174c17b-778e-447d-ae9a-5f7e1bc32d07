// services/PriceService.js
class PriceService {
  constructor() {
    this.db = wx.cloud.database()
    this.cache = new Map() // 价格缓存
    this.cacheExpiry = 5 * 60 * 1000 // 5分钟缓存过期时间
    this.maxRetries = 3 // 最大重试次数
    this.retryDelay = 1000 // 重试延迟（毫秒）
    this.logger = this.initLogger()
    this.priceApis = {
      stock: 'https://api.example.com/stock', // 股票价格API
      fund: 'https://api.example.com/fund'    // 基金价格API
    }
  }

  // 初始化日志记录器
  initLogger() {
    return {
      info: (message, data = {}) => {
        console.log(`[PriceService INFO] ${message}`, data)
        this.saveLog('info', message, data)
      },
      error: (message, error = {}) => {
        console.error(`[PriceService ERROR] ${message}`, error)
        this.saveLog('error', message, error)
      },
      warn: (message, data = {}) => {
        console.warn(`[PriceService WARN] ${message}`, data)
        this.saveLog('warn', message, data)
      }
    }
  }

  // 保存日志到云数据库
  async saveLog(level, message, data) {
    try {
      await this.db.collection('logs').add({
        data: {
          level,
          message,
          data: JSON.stringify(data),
          timestamp: new Date(),
          source: 'PriceService'
        }
      })
    } catch (error) {
      console.error('保存日志失败:', error)
    }
  }

  // 错误处理包装器
  async executeWithErrorHandling(operation, operationName) {
    try {
      this.logger.info(`开始执行操作: ${operationName}`)
      const result = await operation()
      this.logger.info(`操作成功完成: ${operationName}`, { result })
      return { success: true, data: result }
    } catch (error) {
      this.logger.error(`操作失败: ${operationName}`, error)
      return { 
        success: false, 
        error: {
          message: error.message || '操作失败',
          code: error.code || 'UNKNOWN_ERROR',
          details: error
        }
      }
    }
  }

  // 重试机制
  async executeWithRetry(operation, operationName, maxRetries = this.maxRetries) {
    let lastError
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.info(`执行操作 ${operationName}，第 ${attempt} 次尝试`)
        const result = await operation()
        
        if (attempt > 1) {
          this.logger.info(`操作 ${operationName} 在第 ${attempt} 次尝试后成功`)
        }
        
        return result
      } catch (error) {
        lastError = error
        this.logger.warn(`操作 ${operationName} 第 ${attempt} 次尝试失败`, error)
        
        if (attempt < maxRetries) {
          await this.delay(this.retryDelay * attempt) // 递增延迟
        }
      }
    }
    
    throw lastError
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 获取当前价格
  async getCurrentPrices(symbols) {
    return await this.executeWithErrorHandling(async () => {
      // 输入验证
      if (!symbols) {
        throw new Error('证券代码不能为空')
      }

      if (!Array.isArray(symbols)) {
        symbols = [symbols]
      }

      // 验证证券代码格式
      symbols.forEach(symbol => {
        if (!this.validateSymbol(symbol)) {
          throw new Error(`证券代码格式不正确: ${symbol}`)
        }
      })

      // 检查缓存
      const cachedPrices = this.getCachedPrices(symbols)
      const uncachedSymbols = symbols.filter(symbol => !cachedPrices[symbol])

      let prices = { ...cachedPrices }

      // 获取未缓存的价格
      if (uncachedSymbols.length > 0) {
        const newPrices = await this.executeWithRetry(async () => {
          const result = await wx.cloud.callFunction({
            name: 'getPrices',
            data: {
              symbols: uncachedSymbols
            }
          })

          if (!result.result.success) {
            throw new Error(result.result.error || '获取价格失败')
          }

          return result.result.data
        }, 'getPricesFromCloud')
        
        // 更新缓存
        Object.keys(newPrices).forEach(symbol => {
          this.cache.set(symbol, {
            data: newPrices[symbol],
            timestamp: Date.now()
          })
        })

        // 保存到数据库缓存
        await this.savePricesToDatabase(newPrices)

        prices = { ...prices, ...newPrices }
      }

      return prices
    }, 'getCurrentPrices')
  }

  // 验证证券代码格式
  validateSymbol(symbol) {
    if (typeof symbol !== 'string') {
      return false
    }
    
    // 股票代码：6位数字
    if (/^[0-9]{6}$/.test(symbol)) {
      return true
    }
    
    // 基金代码：6位数字
    if (/^[0-9]{6}$/.test(symbol)) {
      return true
    }
    
    return false
  }

  // 保存价格到数据库
  async savePricesToDatabase(prices) {
    try {
      const batch = []
      const now = new Date()
      
      Object.keys(prices).forEach(symbol => {
        batch.push({
          symbol,
          price: prices[symbol].price,
          change: prices[symbol].change,
          changePercent: prices[symbol].changePercent,
          volume: prices[symbol].volume,
          date: now,
          source: 'api'
        })
      })

      if (batch.length > 0) {
        await this.db.collection('prices').add({
          data: batch
        })
      }
    } catch (error) {
      this.logger.error('保存价格到数据库失败', error)
    }
  }

  // 获取历史价格
  async getHistoricalPrices(symbol, startDate, endDate) {
    return await this.executeWithErrorHandling(async () => {
      // 输入验证
      if (!symbol) {
        throw new Error('证券代码不能为空')
      }
      if (!this.validateSymbol(symbol)) {
        throw new Error('证券代码格式不正确')
      }
      if (!startDate || !endDate) {
        throw new Error('开始日期和结束日期不能为空')
      }
      if (new Date(startDate) > new Date(endDate)) {
        throw new Error('开始日期不能晚于结束日期')
      }

      // 先尝试从数据库获取历史数据
      const cachedData = await this.getHistoricalPricesFromDatabase(symbol, startDate, endDate)
      if (cachedData && cachedData.length > 0) {
        this.logger.info('从数据库获取历史价格', { symbol, count: cachedData.length })
        return cachedData
      }

      // 从云函数获取
      const result = await this.executeWithRetry(async () => {
        const cloudResult = await wx.cloud.callFunction({
          name: 'getHistoricalPrices',
          data: {
            symbol,
            startDate,
            endDate
          }
        })

        if (!cloudResult.result.success) {
          throw new Error(cloudResult.result.error || '获取历史价格失败')
        }

        return cloudResult.result.data
      }, 'getHistoricalPricesFromCloud')

      // 保存到数据库
      if (result && result.length > 0) {
        await this.saveHistoricalPricesToDatabase(symbol, result)
      }

      return result
    }, 'getHistoricalPrices')
  }

  // 从数据库获取历史价格
  async getHistoricalPricesFromDatabase(symbol, startDate, endDate) {
    try {
      const result = await this.db.collection('historical_prices')
        .where({
          symbol: symbol,
          date: this.db.command.gte(new Date(startDate)).and(this.db.command.lte(new Date(endDate)))
        })
        .orderBy('date', 'asc')
        .get()

      return result.data
    } catch (error) {
      this.logger.error('从数据库获取历史价格失败', error)
      return []
    }
  }

  // 保存历史价格到数据库
  async saveHistoricalPricesToDatabase(symbol, priceData) {
    try {
      const batch = priceData.map(item => ({
        symbol,
        date: new Date(item.date),
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
        createdAt: new Date()
      }))

      if (batch.length > 0) {
        // 分批插入，避免单次插入数据过多
        const batchSize = 20
        for (let i = 0; i < batch.length; i += batchSize) {
          const chunk = batch.slice(i, i + batchSize)
          await this.db.collection('historical_prices').add({
            data: chunk
          })
        }
      }
    } catch (error) {
      this.logger.error('保存历史价格到数据库失败', error)
    }
  }

  // 获取基金数据
  async getFundData(fundCode) {
    return await this.executeWithErrorHandling(async () => {
      // 输入验证
      if (!fundCode) {
        throw new Error('基金代码不能为空')
      }
      if (!this.validateSymbol(fundCode)) {
        throw new Error('基金代码格式不正确')
      }

      // 检查缓存
      const cacheKey = `fund_${fundCode}`
      const cached = this.cache.get(cacheKey)
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        this.logger.info('从缓存获取基金数据', { fundCode })
        return cached.data
      }

      // 从云函数获取
      const result = await this.executeWithRetry(async () => {
        const cloudResult = await wx.cloud.callFunction({
          name: 'getFundData',
          data: {
            fundCode
          }
        })

        if (!cloudResult.result.success) {
          throw new Error(cloudResult.result.error || '获取基金数据失败')
        }

        return cloudResult.result.data
      }, 'getFundDataFromCloud')

      // 更新缓存
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      })

      // 保存到数据库
      await this.saveFundDataToDatabase(fundCode, result)

      return result
    }, 'getFundData')
  }

  // 保存基金数据到数据库
  async saveFundDataToDatabase(fundCode, fundData) {
    try {
      await this.db.collection('fund_data').add({
        data: {
          fundCode,
          name: fundData.name,
          netValue: fundData.netValue,
          accumulatedValue: fundData.accumulatedValue,
          dailyChange: fundData.dailyChange,
          dailyChangePercent: fundData.dailyChangePercent,
          fundType: fundData.fundType,
          fundCompany: fundData.fundCompany,
          date: new Date(),
          createdAt: new Date()
        }
      })
    } catch (error) {
      this.logger.error('保存基金数据到数据库失败', error)
    }
  }

  // 更新价格缓存
  async updatePriceCache() {
    return await this.executeWithErrorHandling(async () => {
      // 获取所有需要更新的证券代码
      const symbols = await this.getAllSymbols()
      
      if (symbols.length === 0) {
        this.logger.info('没有需要更新的证券代码')
        return { message: '没有需要更新的证券代码', updated: 0 }
      }

      this.logger.info('开始更新价格缓存', { symbolCount: symbols.length })

      // 分批更新，避免单次请求过多
      const batchSize = 10
      let updatedCount = 0
      
      for (let i = 0; i < symbols.length; i += batchSize) {
        const batch = symbols.slice(i, i + batchSize)
        
        try {
          const result = await this.getCurrentPrices(batch)
          if (result.success) {
            updatedCount += batch.length
            this.logger.info(`批次更新成功`, { batch: batch.length, total: updatedCount })
          }
        } catch (error) {
          this.logger.error('批次更新失败', { batch, error })
        }
        
        // 批次间延迟，避免请求过于频繁
        if (i + batchSize < symbols.length) {
          await this.delay(500)
        }
      }

      this.logger.info('价格缓存更新完成', { total: symbols.length, updated: updatedCount })
      return { message: '价格缓存更新完成', total: symbols.length, updated: updatedCount }
    }, 'updatePriceCache')
  }

  // 获取缓存的价格
  getCachedPrices(symbols) {
    const cachedPrices = {}
    const now = Date.now()
    
    symbols.forEach(symbol => {
      const cached = this.cache.get(symbol)
      if (cached && (now - cached.timestamp) < this.cacheExpiry) {
        cachedPrices[symbol] = cached.data
      }
    })

    this.logger.info('缓存命中情况', { 
      total: symbols.length, 
      cached: Object.keys(cachedPrices).length 
    })

    return cachedPrices
  }

  // 预热缓存
  async warmupCache(symbols) {
    return await this.executeWithErrorHandling(async () => {
      if (!symbols || symbols.length === 0) {
        symbols = await this.getAllSymbols()
      }

      this.logger.info('开始预热缓存', { symbolCount: symbols.length })

      const result = await this.getCurrentPrices(symbols)
      if (result.success) {
        return { message: '缓存预热完成', symbolCount: symbols.length }
      } else {
        throw new Error('缓存预热失败')
      }
    }, 'warmupCache')
  }

  // 获取所有证券代码
  async getAllSymbols() {
    try {
      // 获取持仓中的证券代码
      const positionsResult = await this.db.collection('positions')
        .field({
          symbol: true
        })
        .get()

      // 获取基金代码
      const fundsResult = await this.db.collection('fund_positions')
        .field({
          fundCode: true
        })
        .get()

      const stockSymbols = positionsResult.data.map(item => item.symbol)
      const fundSymbols = fundsResult.data.map(item => item.fundCode)
      
      const allSymbols = [...new Set([...stockSymbols, ...fundSymbols])]
      
      this.logger.info('获取所有证券代码', { 
        stocks: stockSymbols.length, 
        funds: fundSymbols.length, 
        total: allSymbols.length 
      })
      
      return allSymbols
    } catch (error) {
      this.logger.error('获取证券代码失败', error)
      return []
    }
  }

  // 清除缓存
  clearCache() {
    const size = this.cache.size
    this.cache.clear()
    this.logger.info('缓存已清除', { previousSize: size })
  }

  // 清除过期缓存
  clearExpiredCache() {
    const now = Date.now()
    let clearedCount = 0
    
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheExpiry) {
        this.cache.delete(key)
        clearedCount++
      }
    }
    
    this.logger.info('过期缓存已清除', { clearedCount, remainingSize: this.cache.size })
    return clearedCount
  }

  // 获取缓存状态
  getCacheStatus() {
    const now = Date.now()
    const cacheInfo = {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      expiredCount: 0,
      validCount: 0
    }

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheExpiry) {
        cacheInfo.expiredCount++
      } else {
        cacheInfo.validCount++
      }
    }

    return cacheInfo
  }

  // 获取价格更新统计
  async getPriceUpdateStats(days = 7) {
    return await this.executeWithErrorHandling(async () => {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const result = await this.db.collection('prices')
        .where({
          date: this.db.command.gte(startDate)
        })
        .count()

      return {
        period: `${days}天`,
        updateCount: result.total,
        averagePerDay: Math.round(result.total / days)
      }
    }, 'getPriceUpdateStats')
  }

  // 检查价格服务健康状态
  async checkHealth() {
    return await this.executeWithErrorHandling(async () => {
      const cacheStatus = this.getCacheStatus()
      const symbols = await this.getAllSymbols()
      
      // 测试获取一个价格
      let priceTestResult = null
      if (symbols.length > 0) {
        const testResult = await this.getCurrentPrices([symbols[0]])
        priceTestResult = testResult.success
      }

      return {
        cache: {
          size: cacheStatus.size,
          validCount: cacheStatus.validCount,
          expiredCount: cacheStatus.expiredCount
        },
        symbols: {
          total: symbols.length
        },
        priceTest: priceTestResult,
        timestamp: new Date()
      }
    }, 'checkHealth')
  }
}

// 导出单例
const priceService = new PriceService()
export default priceService