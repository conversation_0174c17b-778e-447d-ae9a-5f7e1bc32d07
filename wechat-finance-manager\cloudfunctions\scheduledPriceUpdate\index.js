// cloudfunctions/scheduledPriceUpdate/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 配置
const CONFIG = {
  batchSize: 20, // 每批处理的证券数量
  batchDelay: 2000, // 批次间延迟（毫秒）
  maxRetries: 3,
  retryDelay: 5000,
  tradingHours: {
    start: '09:30',
    end: '15:00'
  },
  tradingDays: [1, 2, 3, 4, 5] // 周一到周五
}

// 日志记录
function log(level, message, data = {}) {
  const logData = {
    level,
    message,
    data: JSON.stringify(data),
    timestamp: new Date(),
    source: 'scheduledPriceUpdate'
  }
  
  console.log(`[${level.toUpperCase()}] ${message}`, data)
  
  db.collection('logs').add({ data: logData }).catch(err => {
    console.error('保存日志失败:', err)
  })
}

// 延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 检查是否为交易时间
function isTradingTime() {
  const now = new Date()
  const day = now.getDay()
  const time = now.toTimeString().slice(0, 5) // HH:MM格式
  
  // 检查是否为交易日
  if (!CONFIG.tradingDays.includes(day)) {
    return false
  }
  
  // 检查是否在交易时间内
  return time >= CONFIG.tradingHours.start && time <= CONFIG.tradingHours.end
}

// 获取所有需要更新的证券代码
async function getAllSymbols() {
  try {
    // 获取持仓中的证券代码
    const positionsResult = await db.collection('positions')
      .field({
        symbol: true
      })
      .get()
    
    // 获取基金代码
    const fundsResult = await db.collection('fund_positions')
      .field({
        fundCode: true
      })
      .get()
    
    const stockSymbols = positionsResult.data.map(item => item.symbol)
    const fundSymbols = fundsResult.data.map(item => item.fundCode)
    
    const allSymbols = [...new Set([...stockSymbols, ...fundSymbols])]
    
    log('info', '获取所有证券代码', {
      stocks: stockSymbols.length,
      funds: fundSymbols.length,
      total: allSymbols.length
    })
    
    return allSymbols
  } catch (error) {
    log('error', '获取证券代码失败', { error: error.message })
    return []
  }
}

// 批量更新价格
async function batchUpdatePrices(symbols) {
  let totalUpdated = 0
  let totalErrors = 0
  const errors = []
  
  // 分批处理
  for (let i = 0; i < symbols.length; i += CONFIG.batchSize) {
    const batch = symbols.slice(i, i + CONFIG.batchSize)
    
    try {
      log('info', `处理批次 ${Math.floor(i / CONFIG.batchSize) + 1}`, {
        batch: batch.length,
        symbols: batch
      })
      
      // 调用获取价格的云函数
      const result = await cloud.callFunction({
        name: 'getPrices',
        data: {
          symbols: batch,
          forceRefresh: true
        }
      })
      
      if (result.result.success) {
        const priceCount = Object.keys(result.result.data).length
        totalUpdated += priceCount
        
        log('info', '批次更新成功', {
          batch: batch.length,
          updated: priceCount
        })
      } else {
        totalErrors += batch.length
        errors.push({
          batch,
          error: result.result.error
        })
        
        log('error', '批次更新失败', {
          batch,
          error: result.result.error
        })
      }
      
    } catch (error) {
      totalErrors += batch.length
      errors.push({
        batch,
        error: error.message
      })
      
      log('error', '批次处理异常', {
        batch,
        error: error.message
      })
    }
    
    // 批次间延迟
    if (i + CONFIG.batchSize < symbols.length) {
      await delay(CONFIG.batchDelay)
    }
  }
  
  return {
    total: symbols.length,
    updated: totalUpdated,
    errors: totalErrors,
    errorDetails: errors
  }
}

// 记录更新统计
async function recordUpdateStats(stats) {
  try {
    await db.collection('price_update_stats').add({
      data: {
        ...stats,
        timestamp: new Date(),
        tradingTime: isTradingTime()
      }
    })
    
    log('info', '更新统计已记录', stats)
  } catch (error) {
    log('error', '记录更新统计失败', { error: error.message })
  }
}

// 清理过期的价格数据
async function cleanupExpiredPrices() {
  try {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    // 删除30天前的价格数据
    const result = await db.collection('prices')
      .where({
        createdAt: _.lt(thirtyDaysAgo)
      })
      .remove()
    
    log('info', '清理过期价格数据', {
      removed: result.stats.removed,
      cutoffDate: thirtyDaysAgo
    })
    
    return result.stats.removed
  } catch (error) {
    log('error', '清理过期价格数据失败', { error: error.message })
    return 0
  }
}

// 发送更新通知（如果需要）
async function sendUpdateNotification(stats) {
  try {
    // 如果错误率过高，发送警告通知
    const errorRate = stats.errors / stats.total
    
    if (errorRate > 0.5) {
      log('warn', '价格更新错误率过高', {
        errorRate: `${(errorRate * 100).toFixed(2)}%`,
        stats
      })
      
      // 这里可以集成消息推送服务
      // 例如发送邮件或微信消息给管理员
    }
    
    // 记录成功率统计
    await db.collection('system_notifications').add({
      data: {
        type: 'price_update',
        level: errorRate > 0.5 ? 'warning' : 'info',
        message: `价格更新完成，成功率: ${((1 - errorRate) * 100).toFixed(2)}%`,
        stats,
        timestamp: new Date()
      }
    })
    
  } catch (error) {
    log('error', '发送更新通知失败', { error: error.message })
  }
}

// 云函数入口
exports.main = async (event, context) => {
  const { force = false } = event
  
  try {
    log('info', '开始定时价格更新任务', { force, tradingTime: isTradingTime() })
    
    // 如果不是强制更新且不在交易时间，跳过更新
    if (!force && !isTradingTime()) {
      log('info', '非交易时间，跳过价格更新')
      return {
        success: true,
        message: '非交易时间，跳过价格更新',
        timestamp: new Date()
      }
    }
    
    // 获取所有需要更新的证券代码
    const symbols = await getAllSymbols()
    
    if (symbols.length === 0) {
      log('info', '没有需要更新的证券代码')
      return {
        success: true,
        message: '没有需要更新的证券代码',
        timestamp: new Date()
      }
    }
    
    // 批量更新价格
    const updateStats = await batchUpdatePrices(symbols)
    
    // 记录更新统计
    await recordUpdateStats(updateStats)
    
    // 清理过期数据
    const cleanupCount = await cleanupExpiredPrices()
    
    // 发送通知
    await sendUpdateNotification(updateStats)
    
    const result = {
      success: true,
      message: '定时价格更新完成',
      stats: {
        ...updateStats,
        cleanupCount
      },
      timestamp: new Date()
    }
    
    log('info', '定时价格更新任务完成', result.stats)
    
    return result
    
  } catch (error) {
    log('error', '定时价格更新任务失败', { error: error.message, stack: error.stack })
    
    return {
      success: false,
      error: error.message,
      timestamp: new Date()
    }
  }
}