/* pages/import-positions/import-positions.wxss */
@import "../../styles/common.wxss";

.container {
  min-height: 100vh;
  background-color: var(--bg-color);
}

/* 步骤容器 */
.step-container {
  padding: 32rpx;
}

.step-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.step-title {
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.step-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 模板说明 */
.template-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.btn-link {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 28rpx;
  padding: 0;
}

.template-table {
  background-color: var(--card-bg);
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid var(--border-color);
}

.table-header,
.table-row {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
}

.table-header {
  background-color: var(--background-tertiary);
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 28rpx;
}

.table-row {
  border-top: 1rpx solid var(--border-color);
  font-size: 30rpx;
}

.col-name {
  flex: 2;
}

.col-required {
  flex: 1;
  text-align: center;
}

.col-required.required {
  color: var(--error-color);
}

.col-required.optional {
  color: var(--text-tertiary);
}

.col-example {
  flex: 2;
  color: var(--text-secondary);
}

/* 文件选择 */
.file-select-section {
  text-align: center;
}

.btn-large {
  height: 100rpx;
  font-size: 36rpx;
  margin-bottom: 40rpx;
}

.file-tips {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 数据统计 */
.data-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
  padding: 32rpx;
  background-color: var(--card-bg);
  border-radius: 16rpx;
  border: 2rpx solid var(--border-color);
}

.summary-item {
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.summary-value {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.summary-item.success .summary-value {
  color: var(--success-color);
}

.summary-item.error .summary-value {
  color: var(--error-color);
}

/* 数据预览 */
.data-preview,
.error-preview {
  margin-bottom: 40rpx;
}

.preview-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 20rpx;
}

.preview-title.error {
  color: var(--error-color);
}

.preview-table {
  background-color: var(--card-bg);
  border-radius: 12rpx;
  border: 2rpx solid var(--border-color);
  white-space: nowrap;
}

.preview-table .table-header,
.preview-table .table-row {
  display: flex;
  padding: 20rpx 16rpx;
}

.preview-table .col {
  flex: 0 0 160rpx;
  padding: 0 8rpx;
  font-size: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-table .table-header {
  background-color: var(--background-tertiary);
  font-weight: 500;
  color: var(--text-secondary);
}

.preview-table .table-row {
  border-top: 1rpx solid var(--border-color);
  color: var(--text-primary);
}

/* 错误预览 */
.error-item {
  background-color: var(--card-bg);
  border: 2rpx solid var(--error-color);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.error-row {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.error-symbol {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.error-messages {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.error-message {
  font-size: 26rpx;
  color: var(--error-color);
}

.more-errors {
  text-align: center;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-top: 16rpx;
}

/* 导入进度 */
.import-progress {
  text-align: center;
  margin-bottom: 40rpx;
}

.progress-circle {
  width: 200rpx;
  height: 200rpx;
  border: 8rpx solid var(--border-color);
  border-top: 8rpx solid var(--primary-color);
  border-radius: 50%;
  margin: 0 auto 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: spin 1s linear infinite;
}

.progress-text {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--primary-color);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-details {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 20rpx;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.progress-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.progress-value {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.progress-item.success .progress-value {
  color: var(--success-color);
}

.progress-item.error .progress-value {
  color: var(--error-color);
}

/* 导入错误 */
.import-errors,
.final-errors {
  margin-top: 40rpx;
}

.errors-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--error-color);
  margin-bottom: 20rpx;
}

.errors-list {
  background-color: var(--card-bg);
  border-radius: 12rpx;
  border: 2rpx solid var(--error-color);
  padding: 20rpx;
}

.errors-list .error-item {
  background: none;
  border: none;
  padding: 16rpx 0;
  margin-bottom: 0;
  border-bottom: 1rpx solid var(--border-color);
}

.errors-list .error-item:last-child {
  border-bottom: none;
}

/* 结果统计 */
.result-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
  padding: 40rpx;
  background-color: var(--card-bg);
  border-radius: 16rpx;
  border: 2rpx solid var(--border-color);
}

.result-item {
  text-align: center;
}

.result-number {
  font-size: 60rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
}

.result-item.success .result-number {
  color: var(--success-color);
}

.result-item.error .result-number {
  color: var(--error-color);
}

.result-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 按钮容器 */
.button-container {
  display: flex;
  gap: 24rpx;
  margin-top: 60rpx;
}

.btn-block {
  width: 100%;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: var(--card-bg);
  border-radius: 16rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  min-width: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-primary);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .template-table,
  .preview-table,
  .data-summary,
  .result-summary,
  .error-item,
  .errors-list,
  .loading-content {
    background-color: var(--card-bg-dark);
    border-color: var(--border-color-dark);
  }
  
  .table-header {
    background-color: var(--background-tertiary);
  }
}