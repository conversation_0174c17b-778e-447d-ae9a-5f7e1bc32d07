<!--pages/index/index.wxml-->
<view class="container page-transition {{pageTransition}}">
  <!-- 总资产概览 -->
  <asset-overview 
    id="asset-overview"
    user-id="{{userId}}"
    theme="{{theme}}"
    auto-refresh="{{true}}"
    refresh-interval="{{30000}}"
    bind:dataUpdated="onAssetDataUpdated"
    bind:error="onAssetError"
    bind:refresh="onAssetRefresh"
    bind:cardTap="onAssetCardTap"
    bind:viewDetail="onViewAssetDetail">
  </asset-overview>

  <!-- 快捷操作 -->
  <view class="quick-actions-section">
    <view class="section-title">快捷操作</view>
    <view class="quick-actions">
      <view class="action-item" bindtap="navigateToCharts">
        <view class="action-icon">📊</view>
        <view class="action-text">资产图表</view>
      </view>
      <view class="action-item" bindtap="navigateToAddTransaction">
        <view class="action-icon">📈</view>
        <view class="action-text">添加交易</view>
      </view>
      <view class="action-item" bindtap="navigateToAddPosition">
        <view class="action-icon">💰</view>
        <view class="action-text">添加持仓</view>
      </view>
      <view class="action-item" bindtap="navigateToAddAccount">
        <view class="action-icon">🏦</view>
        <view class="action-text">添加账户</view>
      </view>
    </view>
  </view>

  <!-- 账户概览 -->
  <view class="accounts-section" wx:if="{{accounts.length > 0}}">
    <view class="section-title">
      <text>我的账户</text>
      <text class="view-all" bindtap="navigateToAccounts">查看全部</text>
    </view>
    <view class="accounts-preview">
      <view class="account-item" wx:for="{{accounts}}" wx:key="id" bindtap="navigateToAccountDetail" data-id="{{item.id}}">
        <view class="account-info">
          <view class="account-name">{{item.brokerName}}</view>
          <view class="account-number">{{item.accountNumber}}</view>
        </view>
        <view class="account-value">
          <view class="value-amount">¥{{item.totalValue}}</view>
          <view class="value-change {{item.dailyChange >= 0 ? 'positive' : 'negative'}}">
            {{item.dailyChange >= 0 ? '+' : ''}}{{item.dailyChange}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && accounts.length === 0}}">
    <view class="empty-icon">📊</view>
    <view class="empty-title">暂无账户数据</view>
    <view class="empty-subtitle">添加您的第一个证券账户开始管理资产</view>
    <button class="btn btn-primary" bindtap="navigateToAddAccount">添加账户</button>
  </view>
</view>