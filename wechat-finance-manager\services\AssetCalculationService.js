// services/AssetCalculationService.js
import priceService from './PriceService.js'

class AssetCalculationService {
  constructor() {
    this.db = wx.cloud.database()
    this.cache = new Map()
    this.cacheExpiry = 2 * 60 * 1000 // 2分钟缓存过期时间
    this.logger = this.initLogger()
  }

  // 初始化日志记录器
  initLogger() {
    return {
      info: (message, data = {}) => {
        console.log(`[AssetCalculationService INFO] ${message}`, data)
        this.saveLog('info', message, data)
      },
      error: (message, error = {}) => {
        console.error(`[AssetCalculationService ERROR] ${message}`, error)
        this.saveLog('error', message, error)
      },
      warn: (message, data = {}) => {
        console.warn(`[AssetCalculationService WARN] ${message}`, data)
        this.saveLog('warn', message, data)
      }
    }
  }

  // 保存日志到云数据库
  async saveLog(level, message, data) {
    try {
      await this.db.collection('logs').add({
        data: {
          level,
          message,
          data: JSON.stringify(data),
          timestamp: new Date(),
          source: 'AssetCalculationService'
        }
      })
    } catch (error) {
      console.error('保存日志失败:', error)
    }
  }

  // 错误处理包装器
  async executeWithErrorHandling(operation, operationName) {
    try {
      this.logger.info(`开始执行操作: ${operationName}`)
      const result = await operation()
      this.logger.info(`操作成功完成: ${operationName}`, { result })
      return { success: true, data: result }
    } catch (error) {
      this.logger.error(`操作失败: ${operationName}`, error)
      return { 
        success: false, 
        error: {
          message: error.message || '操作失败',
          code: error.code || 'UNKNOWN_ERROR',
          details: error
        }
      }
    }
  }

  // 计算单个持仓的市值
  async calculatePositionValue(position, currentPrice) {
    try {
      if (!position || !position.quantity || !currentPrice) {
        throw new Error('持仓数据或价格数据无效')
      }

      const quantity = parseFloat(position.quantity)
      const costPrice = parseFloat(position.costPrice) || 0
      const price = parseFloat(currentPrice.price) || 0

      const marketValue = quantity * price
      const costValue = quantity * costPrice
      const unrealizedPnL = marketValue - costValue
      const unrealizedPnLPercent = costValue > 0 ? (unrealizedPnL / costValue) * 100 : 0

      return {
        symbol: position.symbol,
        name: currentPrice.name || position.name || position.symbol,
        quantity,
        costPrice,
        currentPrice: price,
        marketValue: parseFloat(marketValue.toFixed(2)),
        costValue: parseFloat(costValue.toFixed(2)),
        unrealizedPnL: parseFloat(unrealizedPnL.toFixed(2)),
        unrealizedPnLPercent: parseFloat(unrealizedPnLPercent.toFixed(2)),
        dailyChange: currentPrice.change || 0,
        dailyChangePercent: currentPrice.changePercent || 0,
        dailyPnL: parseFloat((quantity * (currentPrice.change || 0)).toFixed(2))
      }
    } catch (error) {
      this.logger.error('计算持仓市值失败', { position, currentPrice, error: error.message })
      throw error
    }
  }

  // 计算账户总资产
  async calculateAccountAssets(accountId) {
    return await this.executeWithErrorHandling(async () => {
      this.logger.info('开始计算账户资产', { accountId })

      // 获取账户持仓
      const positionsResult = await this.db.collection('positions')
        .where({
          accountId: accountId
        })
        .get()

      if (positionsResult.data.length === 0) {
        this.logger.info('账户无持仓数据', { accountId })
        return {
          accountId,
          totalMarketValue: 0,
          totalCostValue: 0,
          totalUnrealizedPnL: 0,
          totalUnrealizedPnLPercent: 0,
          totalDailyPnL: 0,
          totalDailyPnLPercent: 0,
          positions: [],
          lastUpdated: new Date()
        }
      }

      const positions = positionsResult.data
      const symbols = positions.map(p => p.symbol)

      // 获取当前价格
      const pricesResult = await priceService.getCurrentPrices(symbols)
      if (!pricesResult.success) {
        throw new Error(`获取价格失败: ${pricesResult.error.message}`)
      }

      const prices = pricesResult.data
      const positionValues = []
      let totalMarketValue = 0
      let totalCostValue = 0
      let totalDailyPnL = 0

      // 计算每个持仓的价值
      for (const position of positions) {
        const currentPrice = prices[position.symbol]
        
        if (!currentPrice) {
          this.logger.warn('未获取到价格数据', { symbol: position.symbol })
          continue
        }

        const positionValue = await this.calculatePositionValue(position, currentPrice)
        positionValues.push(positionValue)

        totalMarketValue += positionValue.marketValue
        totalCostValue += positionValue.costValue
        totalDailyPnL += positionValue.dailyPnL
      }

      const totalUnrealizedPnL = totalMarketValue - totalCostValue
      const totalUnrealizedPnLPercent = totalCostValue > 0 ? (totalUnrealizedPnL / totalCostValue) * 100 : 0
      const totalDailyPnLPercent = totalMarketValue > 0 ? (totalDailyPnL / (totalMarketValue - totalDailyPnL)) * 100 : 0

      const result = {
        accountId,
        totalMarketValue: parseFloat(totalMarketValue.toFixed(2)),
        totalCostValue: parseFloat(totalCostValue.toFixed(2)),
        totalUnrealizedPnL: parseFloat(totalUnrealizedPnL.toFixed(2)),
        totalUnrealizedPnLPercent: parseFloat(totalUnrealizedPnLPercent.toFixed(2)),
        totalDailyPnL: parseFloat(totalDailyPnL.toFixed(2)),
        totalDailyPnLPercent: parseFloat(totalDailyPnLPercent.toFixed(2)),
        positions: positionValues,
        lastUpdated: new Date()
      }

      this.logger.info('账户资产计算完成', {
        accountId,
        totalMarketValue: result.totalMarketValue,
        positionCount: positionValues.length
      })

      return result
    }, 'calculateAccountAssets')
  }

  // 计算用户总资产
  async calculateUserTotalAssets(userId) {
    return await this.executeWithErrorHandling(async () => {
      this.logger.info('开始计算用户总资产', { userId })

      // 获取用户所有账户
      const accountsResult = await this.db.collection('accounts')
        .where({
          userId: userId,
          isActive: true
        })
        .get()

      if (accountsResult.data.length === 0) {
        this.logger.info('用户无活跃账户', { userId })
        return {
          userId,
          totalMarketValue: 0,
          totalCostValue: 0,
          totalUnrealizedPnL: 0,
          totalUnrealizedPnLPercent: 0,
          totalDailyPnL: 0,
          totalDailyPnLPercent: 0,
          accounts: [],
          lastUpdated: new Date()
        }
      }

      const accounts = accountsResult.data
      const accountAssets = []
      let totalMarketValue = 0
      let totalCostValue = 0
      let totalDailyPnL = 0

      // 计算每个账户的资产
      for (const account of accounts) {
        const accountAssetResult = await this.calculateAccountAssets(account._id)
        
        if (accountAssetResult.success) {
          const accountAsset = {
            ...accountAssetResult.data,
            accountInfo: {
              _id: account._id,
              brokerName: account.brokerName,
              accountNumber: account.accountNumber,
              accountType: account.accountType
            }
          }
          
          accountAssets.push(accountAsset)
          totalMarketValue += accountAsset.totalMarketValue
          totalCostValue += accountAsset.totalCostValue
          totalDailyPnL += accountAsset.totalDailyPnL
        } else {
          this.logger.error('计算账户资产失败', {
            accountId: account._id,
            error: accountAssetResult.error
          })
        }
      }

      const totalUnrealizedPnL = totalMarketValue - totalCostValue
      const totalUnrealizedPnLPercent = totalCostValue > 0 ? (totalUnrealizedPnL / totalCostValue) * 100 : 0
      const totalDailyPnLPercent = totalMarketValue > 0 ? (totalDailyPnL / (totalMarketValue - totalDailyPnL)) * 100 : 0

      const result = {
        userId,
        totalMarketValue: parseFloat(totalMarketValue.toFixed(2)),
        totalCostValue: parseFloat(totalCostValue.toFixed(2)),
        totalUnrealizedPnL: parseFloat(totalUnrealizedPnL.toFixed(2)),
        totalUnrealizedPnLPercent: parseFloat(totalUnrealizedPnLPercent.toFixed(2)),
        totalDailyPnL: parseFloat(totalDailyPnL.toFixed(2)),
        totalDailyPnLPercent: parseFloat(totalDailyPnLPercent.toFixed(2)),
        accounts: accountAssets,
        lastUpdated: new Date()
      }

      this.logger.info('用户总资产计算完成', {
        userId,
        totalMarketValue: result.totalMarketValue,
        accountCount: accountAssets.length
      })

      return result
    }, 'calculateUserTotalAssets')
  }

  // 获取缓存的资产数据
  getCachedAssets(key) {
    const cached = this.cache.get(key)
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      this.logger.info('使用缓存的资产数据', { key })
      return cached.data
    }
    return null
  }

  // 设置资产数据缓存
  setCachedAssets(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
    this.logger.info('资产数据已缓存', { key })
  }

  // 获取账户资产（带缓存）
  async getAccountAssets(accountId, useCache = true, forceRefresh = false) {
    return await this.executeWithErrorHandling(async () => {
      const cacheKey = `account_${accountId}`
      
      if (useCache && !forceRefresh) {
        const cached = this.getCachedAssets(cacheKey)
        if (cached) {
          return cached
        }
      }

      // 调用云函数计算资产
      const result = await wx.cloud.callFunction({
        name: 'calculateAssets',
        data: {
          type: 'account',
          id: accountId,
          useCache: useCache,
          forceRefresh: forceRefresh
        }
      })

      if (!result.result.success) {
        throw new Error(result.result.error || '计算账户资产失败')
      }

      const assetData = result.result.data
      this.setCachedAssets(cacheKey, assetData)

      return assetData
    }, 'getAccountAssets')
  }

  // 获取用户总资产（带缓存）
  async getUserTotalAssets(userId, useCache = true, forceRefresh = false) {
    return await this.executeWithErrorHandling(async () => {
      const cacheKey = `user_${userId}`
      
      if (useCache && !forceRefresh) {
        const cached = this.getCachedAssets(cacheKey)
        if (cached) {
          return cached
        }
      }

      // 调用云函数计算资产
      const result = await wx.cloud.callFunction({
        name: 'calculateAssets',
        data: {
          type: 'user',
          id: userId,
          useCache: useCache,
          forceRefresh: forceRefresh
        }
      })

      if (!result.result.success) {
        throw new Error(result.result.error || '计算用户总资产失败')
      }

      const assetData = result.result.data
      this.setCachedAssets(cacheKey, assetData)

      return assetData
    }, 'getUserTotalAssets')
  }

  // 保存资产快照到数据库
  async saveAssetSnapshot(type, id, assetData) {
    try {
      await this.db.collection('asset_snapshots').add({
        data: {
          type, // 'account' 或 'user'
          targetId: id,
          ...assetData,
          createdAt: new Date()
        }
      })
      
      this.logger.info('资产快照已保存', { type, id })
    } catch (error) {
      this.logger.error('保存资产快照失败', { type, id, error: error.message })
    }
  }

  // 获取资产历史数据
  async getAssetHistory(type, id, days = 30) {
    return await this.executeWithErrorHandling(async () => {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const result = await this.db.collection('asset_snapshots')
        .where({
          type: type,
          targetId: id,
          createdAt: this.db.command.gte(startDate)
        })
        .orderBy('createdAt', 'asc')
        .get()

      this.logger.info('获取资产历史数据', {
        type,
        id,
        days,
        count: result.data.length
      })

      return result.data
    }, 'getAssetHistory')
  }

  // 计算资产变化趋势
  async calculateAssetTrend(type, id, days = 7) {
    return await this.executeWithErrorHandling(async () => {
      const historyResult = await this.getAssetHistory(type, id, days)
      
      if (!historyResult.success || historyResult.data.length < 2) {
        return {
          trend: 'stable',
          changeAmount: 0,
          changePercent: 0,
          dataPoints: historyResult.success ? historyResult.data.length : 0
        }
      }

      const history = historyResult.data
      const latest = history[history.length - 1]
      const earliest = history[0]

      const changeAmount = latest.totalMarketValue - earliest.totalMarketValue
      const changePercent = earliest.totalMarketValue > 0 ? 
        (changeAmount / earliest.totalMarketValue) * 100 : 0

      let trend = 'stable'
      if (changePercent > 1) {
        trend = 'up'
      } else if (changePercent < -1) {
        trend = 'down'
      }

      return {
        trend,
        changeAmount: parseFloat(changeAmount.toFixed(2)),
        changePercent: parseFloat(changePercent.toFixed(2)),
        dataPoints: history.length,
        period: days
      }
    }, 'calculateAssetTrend')
  }

  // 清除缓存
  clearCache() {
    const size = this.cache.size
    this.cache.clear()
    this.logger.info('资产计算缓存已清除', { previousSize: size })
  }

  // 清除过期缓存
  clearExpiredCache() {
    const now = Date.now()
    let clearedCount = 0
    
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheExpiry) {
        this.cache.delete(key)
        clearedCount++
      }
    }
    
    this.logger.info('过期缓存已清除', { clearedCount, remainingSize: this.cache.size })
    return clearedCount
  }

  // 批量更新用户资产
  async batchUpdateUserAssets(userIds) {
    return await this.executeWithErrorHandling(async () => {
      const results = []
      let successCount = 0
      let errorCount = 0

      for (const userId of userIds) {
        try {
          const result = await this.getUserTotalAssets(userId, false) // 强制刷新
          
          if (result.success) {
            results.push({
              userId,
              success: true,
              data: result.data
            })
            successCount++
          } else {
            results.push({
              userId,
              success: false,
              error: result.error
            })
            errorCount++
          }
        } catch (error) {
          results.push({
            userId,
            success: false,
            error: { message: error.message }
          })
          errorCount++
        }
      }

      this.logger.info('批量更新用户资产完成', {
        total: userIds.length,
        success: successCount,
        error: errorCount
      })

      return {
        total: userIds.length,
        success: successCount,
        error: errorCount,
        results
      }
    }, 'batchUpdateUserAssets')
  }

  // 获取缓存状态
  getCacheStatus() {
    const now = Date.now()
    const cacheInfo = {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      expiredCount: 0,
      validCount: 0
    }

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheExpiry) {
        cacheInfo.expiredCount++
      } else {
        cacheInfo.validCount++
      }
    }

    return cacheInfo
  }
}

// 导出单例
const assetCalculationService = new AssetCalculationService()
export default assetCalculationService