// utils/pageTransition.js - 页面过渡动画工具

class PageTransition {
  constructor() {
    this.transitionDuration = 300
    this.currentTransition = null
  }

  // 淡入淡出过渡
  fadeTransition(fromPage, toPage, callback) {
    if (this.currentTransition) {
      return
    }

    this.currentTransition = 'fade'
    
    // 开始淡出动画
    if (fromPage && fromPage.setData) {
      fromPage.setData({
        pageTransition: 'fade-out'
      })
    }

    setTimeout(() => {
      if (callback) callback()
      
      // 开始淡入动画
      setTimeout(() => {
        if (toPage && toPage.setData) {
          toPage.setData({
            pageTransition: 'fade-in'
          })
        }
        
        setTimeout(() => {
          this.currentTransition = null
          if (toPage && toPage.setData) {
            toPage.setData({
              pageTransition: ''
            })
          }
        }, this.transitionDuration)
      }, 50)
    }, this.transitionDuration / 2)
  }

  // 滑动过渡
  slideTransition(direction = 'left', callback) {
    if (this.currentTransition) {
      return
    }

    this.currentTransition = 'slide'
    
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    if (currentPage && currentPage.setData) {
      currentPage.setData({
        pageTransition: `slide-out-${direction}`
      })
    }

    setTimeout(() => {
      if (callback) callback()
      this.currentTransition = null
    }, this.transitionDuration)
  }

  // 缩放过渡
  scaleTransition(callback) {
    if (this.currentTransition) {
      return
    }

    this.currentTransition = 'scale'
    
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    if (currentPage && currentPage.setData) {
      currentPage.setData({
        pageTransition: 'scale-out'
      })
    }

    setTimeout(() => {
      if (callback) callback()
      this.currentTransition = null
    }, this.transitionDuration)
  }

  // 获取页面过渡样式
  getTransitionClass(transition) {
    const transitionClasses = {
      'fade-in': 'page-fade-in',
      'fade-out': 'page-fade-out',
      'slide-out-left': 'page-slide-out-left',
      'slide-out-right': 'page-slide-out-right',
      'slide-in-left': 'page-slide-in-left',
      'slide-in-right': 'page-slide-in-right',
      'scale-out': 'page-scale-out',
      'scale-in': 'page-scale-in'
    }
    
    return transitionClasses[transition] || ''
  }

  // 预设的导航过渡
  navigateWithTransition(url, options = {}) {
    const { 
      transition = 'slide',
      direction = 'left',
      animationType = 'pop-in',
      animationDuration = 300
    } = options

    return new Promise((resolve, reject) => {
      if (transition === 'slide') {
        this.slideTransition(direction, () => {
          wx.navigateTo({
            url,
            animationType,
            animationDuration,
            success: resolve,
            fail: reject
          })
        })
      } else if (transition === 'fade') {
        this.fadeTransition(null, null, () => {
          wx.navigateTo({
            url,
            animationType,
            animationDuration,
            success: resolve,
            fail: reject
          })
        })
      } else {
        wx.navigateTo({
          url,
          animationType,
          animationDuration,
          success: resolve,
          fail: reject
        })
      }
    })
  }

  // Tab 切换过渡
  switchTabWithTransition(url, options = {}) {
    const { transition = 'fade' } = options

    return new Promise((resolve, reject) => {
      if (transition === 'fade') {
        this.fadeTransition(null, null, () => {
          wx.switchTab({
            url,
            success: resolve,
            fail: reject
          })
        })
      } else {
        wx.switchTab({
          url,
          success: resolve,
          fail: reject
        })
      }
    })
  }

  // 返回页面过渡
  navigateBackWithTransition(options = {}) {
    const { 
      transition = 'slide',
      direction = 'right',
      delta = 1
    } = options

    return new Promise((resolve, reject) => {
      if (transition === 'slide') {
        this.slideTransition(direction, () => {
          wx.navigateBack({
            delta,
            success: resolve,
            fail: reject
          })
        })
      } else {
        wx.navigateBack({
          delta,
          success: resolve,
          fail: reject
        })
      }
    })
  }
}

// 创建单例实例
const pageTransition = new PageTransition()

module.exports = pageTransition