// cloudfunctions/getPrices/index.js
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 价格数据源配置
const PRICE_APIS = {
  // 新浪财经API (免费)
  sina: {
    stock: 'https://hq.sinajs.cn/list=',
    fund: 'https://hq.sinajs.cn/list='
  },
  // 腾讯财经API (免费)
  tencent: {
    stock: 'https://qt.gtimg.cn/q=',
    fund: 'https://qt.gtimg.cn/q='
  }
}

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000,
  timeout: 10000
}

// 缓存配置
const CACHE_CONFIG = {
  expiry: 5 * 60 * 1000, // 5分钟
  maxAge: 24 * 60 * 60 * 1000 // 24小时最大缓存时间
}

// 日志记录函数
function log(level, message, data = {}) {
  const logData = {
    level,
    message,
    data: JSON.stringify(data),
    timestamp: new Date(),
    source: 'getPrices'
  }
  
  console.log(`[${level.toUpperCase()}] ${message}`, data)
  
  // 异步保存日志，不影响主流程
  db.collection('logs').add({ data: logData }).catch(err => {
    console.error('保存日志失败:', err)
  })
}

// 延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 重试执行函数
async function executeWithRetry(operation, operationName, maxRetries = RETRY_CONFIG.maxRetries) {
  let lastError
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      log('info', `执行操作 ${operationName}，第 ${attempt} 次尝试`)
      const result = await operation()
      
      if (attempt > 1) {
        log('info', `操作 ${operationName} 在第 ${attempt} 次尝试后成功`)
      }
      
      return result
    } catch (error) {
      lastError = error
      log('warn', `操作 ${operationName} 第 ${attempt} 次尝试失败`, { error: error.message })
      
      if (attempt < maxRetries) {
        await delay(RETRY_CONFIG.retryDelay * attempt)
      }
    }
  }
  
  throw lastError
}

// 验证证券代码
function validateSymbol(symbol) {
  if (typeof symbol !== 'string') return false
  
  // 股票代码：6位数字，支持沪深股市
  if (/^[0-9]{6}$/.test(symbol)) {
    return true
  }
  
  return false
}

// 解析新浪财经数据
function parseSinaData(symbol, data) {
  try {
    if (!data || data === 'N/A') {
      throw new Error('无效的数据格式')
    }
    
    const parts = data.split(',')
    if (parts.length < 32) {
      throw new Error('数据字段不完整')
    }
    
    const name = parts[0]
    const currentPrice = parseFloat(parts[3])
    const previousClose = parseFloat(parts[2])
    const change = currentPrice - previousClose
    const changePercent = previousClose > 0 ? (change / previousClose) * 100 : 0
    const volume = parseInt(parts[8])
    const high = parseFloat(parts[4])
    const low = parseFloat(parts[5])
    const open = parseFloat(parts[1])
    
    return {
      symbol,
      name,
      price: parseFloat(currentPrice.toFixed(2)),
      change: parseFloat(change.toFixed(2)),
      changePercent: parseFloat(changePercent.toFixed(2)),
      volume,
      high: parseFloat(high.toFixed(2)),
      low: parseFloat(low.toFixed(2)),
      open: parseFloat(open.toFixed(2)),
      previousClose: parseFloat(previousClose.toFixed(2)),
      timestamp: new Date(),
      source: 'sina'
    }
  } catch (error) {
    log('error', `解析新浪数据失败: ${symbol}`, { error: error.message, data })
    throw error
  }
}

// 解析腾讯财经数据
function parseTencentData(symbol, data) {
  try {
    if (!data || data === 'N/A') {
      throw new Error('无效的数据格式')
    }
    
    const parts = data.split('~')
    if (parts.length < 50) {
      throw new Error('数据字段不完整')
    }
    
    const name = parts[1]
    const currentPrice = parseFloat(parts[3])
    const previousClose = parseFloat(parts[4])
    const change = parseFloat(parts[31])
    const changePercent = parseFloat(parts[32])
    const volume = parseInt(parts[6])
    const high = parseFloat(parts[33])
    const low = parseFloat(parts[34])
    const open = parseFloat(parts[5])
    
    return {
      symbol,
      name,
      price: parseFloat(currentPrice.toFixed(2)),
      change: parseFloat(change.toFixed(2)),
      changePercent: parseFloat(changePercent.toFixed(2)),
      volume,
      high: parseFloat(high.toFixed(2)),
      low: parseFloat(low.toFixed(2)),
      open: parseFloat(open.toFixed(2)),
      previousClose: parseFloat(previousClose.toFixed(2)),
      timestamp: new Date(),
      source: 'tencent'
    }
  } catch (error) {
    log('error', `解析腾讯数据失败: ${symbol}`, { error: error.message, data })
    throw error
  }
}

// 从新浪财经获取价格
async function getPricesFromSina(symbols) {
  const symbolsWithPrefix = symbols.map(symbol => {
    // 根据代码前缀判断市场
    if (symbol.startsWith('6')) {
      return `sh${symbol}` // 上海证券交易所
    } else if (symbol.startsWith('0') || symbol.startsWith('3')) {
      return `sz${symbol}` // 深圳证券交易所
    }
    return symbol
  })
  
  const url = `${PRICE_APIS.sina.stock}${symbolsWithPrefix.join(',')}`
  
  const response = await axios.get(url, {
    timeout: RETRY_CONFIG.timeout,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Referer': 'https://finance.sina.com.cn/'
    }
  })
  
  const lines = response.data.split('\n').filter(line => line.trim())
  const prices = {}
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const match = line.match(/var hq_str_(.+?)="(.+?)";/)
    
    if (match) {
      const symbolWithPrefix = match[1]
      const data = match[2]
      const originalSymbol = symbols[i]
      
      if (data && data !== 'N/A') {
        try {
          prices[originalSymbol] = parseSinaData(originalSymbol, data)
        } catch (error) {
          log('warn', `解析新浪数据失败: ${originalSymbol}`, { error: error.message })
        }
      }
    }
  }
  
  return prices
}

// 从腾讯财经获取价格
async function getPricesFromTencent(symbols) {
  const symbolsWithPrefix = symbols.map(symbol => {
    if (symbol.startsWith('6')) {
      return `sh${symbol}`
    } else if (symbol.startsWith('0') || symbol.startsWith('3')) {
      return `sz${symbol}`
    }
    return symbol
  })
  
  const url = `${PRICE_APIS.tencent.stock}${symbolsWithPrefix.join(',')}`
  
  const response = await axios.get(url, {
    timeout: RETRY_CONFIG.timeout,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  })
  
  const lines = response.data.split('\n').filter(line => line.trim())
  const prices = {}
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const match = line.match(/v_(.+?)="(.+?)";/)
    
    if (match) {
      const data = match[2]
      const originalSymbol = symbols[i]
      
      if (data && data !== 'N/A') {
        try {
          prices[originalSymbol] = parseTencentData(originalSymbol, data)
        } catch (error) {
          log('warn', `解析腾讯数据失败: ${originalSymbol}`, { error: error.message })
        }
      }
    }
  }
  
  return prices
}

// 从缓存获取价格
async function getCachedPrices(symbols) {
  try {
    const now = new Date()
    const cacheExpiry = new Date(now.getTime() - CACHE_CONFIG.expiry)
    
    const result = await db.collection('prices')
      .where({
        symbol: _.in(symbols),
        createdAt: _.gte(cacheExpiry)
      })
      .orderBy('createdAt', 'desc')
      .get()
    
    const cachedPrices = {}
    const seenSymbols = new Set()
    
    // 获取每个symbol的最新价格
    result.data.forEach(item => {
      if (!seenSymbols.has(item.symbol)) {
        cachedPrices[item.symbol] = item
        seenSymbols.add(item.symbol)
      }
    })
    
    log('info', '缓存命中情况', {
      requested: symbols.length,
      cached: Object.keys(cachedPrices).length
    })
    
    return cachedPrices
  } catch (error) {
    log('error', '获取缓存价格失败', { error: error.message })
    return {}
  }
}

// 保存价格到数据库
async function savePricesToDatabase(prices) {
  try {
    if (Object.keys(prices).length === 0) {
      return
    }
    
    const batch = []
    const now = new Date()
    const today = now.toISOString().split('T')[0]
    
    Object.values(prices).forEach(priceData => {
      batch.push({
        ...priceData,
        date: today,
        createdAt: now
      })
    })
    
    // 分批插入，避免单次插入过多数据
    const batchSize = 20
    for (let i = 0; i < batch.length; i += batchSize) {
      const chunk = batch.slice(i, i + batchSize)
      await db.collection('prices').add({
        data: chunk
      })
    }
    
    log('info', '价格数据保存成功', { count: batch.length })
  } catch (error) {
    log('error', '保存价格数据失败', { error: error.message })
    throw error
  }
}

// 获取实时价格的主函数
async function getRealTimePrices(symbols) {
  const validSymbols = symbols.filter(validateSymbol)
  
  if (validSymbols.length === 0) {
    throw new Error('没有有效的证券代码')
  }
  
  if (validSymbols.length !== symbols.length) {
    log('warn', '部分证券代码格式无效', {
      total: symbols.length,
      valid: validSymbols.length,
      invalid: symbols.filter(s => !validateSymbol(s))
    })
  }
  
  let prices = {}
  let errors = []
  
  // 尝试从新浪财经获取
  try {
    log('info', '尝试从新浪财经获取价格', { symbols: validSymbols })
    prices = await executeWithRetry(
      () => getPricesFromSina(validSymbols),
      'getPricesFromSina'
    )
    
    if (Object.keys(prices).length > 0) {
      log('info', '新浪财经获取成功', { count: Object.keys(prices).length })
      return prices
    }
  } catch (error) {
    errors.push({ source: 'sina', error: error.message })
    log('warn', '新浪财经获取失败', { error: error.message })
  }
  
  // 如果新浪失败，尝试腾讯财经
  try {
    log('info', '尝试从腾讯财经获取价格', { symbols: validSymbols })
    prices = await executeWithRetry(
      () => getPricesFromTencent(validSymbols),
      'getPricesFromTencent'
    )
    
    if (Object.keys(prices).length > 0) {
      log('info', '腾讯财经获取成功', { count: Object.keys(prices).length })
      return prices
    }
  } catch (error) {
    errors.push({ source: 'tencent', error: error.message })
    log('warn', '腾讯财经获取失败', { error: error.message })
  }
  
  // 如果都失败，抛出错误
  throw new Error(`所有价格源都失败: ${errors.map(e => `${e.source}: ${e.error}`).join('; ')}`)
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { symbols, forceRefresh = false } = event
  
  try {
    log('info', '开始获取价格', { symbols, forceRefresh })
    
    if (!symbols || !Array.isArray(symbols) || symbols.length === 0) {
      throw new Error('证券代码参数无效')
    }
    
    let prices = {}
    
    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      const cachedPrices = await getCachedPrices(symbols)
      const cachedSymbols = Object.keys(cachedPrices)
      const uncachedSymbols = symbols.filter(symbol => !cachedSymbols.includes(symbol))
      
      prices = { ...cachedPrices }
      
      // 只获取未缓存的价格
      if (uncachedSymbols.length > 0) {
        log('info', '获取未缓存的价格', { uncachedSymbols })
        const newPrices = await getRealTimePrices(uncachedSymbols)
        prices = { ...prices, ...newPrices }
        
        // 保存新获取的价格
        await savePricesToDatabase(newPrices)
      }
    } else {
      // 强制刷新，直接获取实时价格
      log('info', '强制刷新价格')
      prices = await getRealTimePrices(symbols)
      await savePricesToDatabase(prices)
    }
    
    // 检查是否所有请求的symbol都有数据
    const missingSymbols = symbols.filter(symbol => !prices[symbol])
    if (missingSymbols.length > 0) {
      log('warn', '部分证券代码未获取到价格', { missingSymbols })
    }
    
    log('info', '价格获取完成', {
      requested: symbols.length,
      obtained: Object.keys(prices).length,
      missing: missingSymbols.length
    })
    
    return {
      success: true,
      data: prices,
      meta: {
        requested: symbols.length,
        obtained: Object.keys(prices).length,
        missing: missingSymbols,
        timestamp: new Date()
      }
    }
    
  } catch (error) {
    log('error', '获取价格失败', { error: error.message, stack: error.stack })
    
    return {
      success: false,
      error: error.message,
      meta: {
        timestamp: new Date()
      }
    }
  }
}