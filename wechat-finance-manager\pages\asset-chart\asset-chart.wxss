/* pages/asset-chart/asset-chart.wxss */
.page-container {
  min-height: 100vh;
  background-color: var(--background-secondary);
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  background-color: var(--background-primary);
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: var(--background-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background-color: var(--background-tertiary);
}

.action-icon {
  font-size: 24rpx;
}

/* 资产概览 */
.asset-overview {
  background-color: var(--background-primary);
  margin: 0 30rpx 30rpx;
  border-radius: var(--border-radius);
  padding: 40rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.overview-main {
  text-align: center;
}

.total-assets {
  margin-bottom: 20rpx;
}

.assets-label {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 10rpx;
}

.assets-value {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.daily-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.daily-change.positive {
  color: var(--success-color);
}

.daily-change.negative {
  color: var(--danger-color);
}

.change-value {
  font-size: 32rpx;
  font-weight: 500;
}

.change-percent {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 时间范围选择器 */
.time-range-selector {
  margin: 0 30rpx 30rpx;
}

.range-scroll {
  white-space: nowrap;
}

.range-list {
  display: inline-flex;
  gap: 20rpx;
  padding: 0 10rpx;
}

.range-item {
  flex-shrink: 0;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  background-color: var(--background-primary);
  border: 2rpx solid var(--border-color);
  transition: all 0.2s ease;
}

.range-item.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.range-item:active {
  transform: scale(0.95);
}

.range-text {
  font-size: 28rpx;
  color: var(--text-primary);
  white-space: nowrap;
}

.range-item.active .range-text {
  color: #ffffff;
}

/* 图表区域 */
.chart-section {
  margin: 0 30rpx 30rpx;
}

.chart-wrapper {
  background-color: var(--background-primary);
  border-radius: var(--border-radius);
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 数据信息 */
.data-info {
  background-color: var(--background-primary);
  margin: 0 30rpx;
  border-radius: var(--border-radius);
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.info-value {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
  transition: all 0.2s ease;
  text-decoration: none;
}

.action-button.primary {
  background-color: var(--primary-color);
  color: #ffffff;
}

.action-button:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 深色模式适配 */
.page-container[data-theme="dark"] {
  background-color: var(--background-secondary);
}

.page-container[data-theme="dark"] .asset-overview,
.page-container[data-theme="dark"] .chart-wrapper,
.page-container[data-theme="dark"] .data-info {
  background-color: var(--background-primary);
  box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.1);
}

.page-container[data-theme="dark"] .range-item {
  background-color: var(--background-primary);
  border-color: var(--border-color);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 30rpx 20rpx 15rpx;
  }
  
  .asset-overview,
  .chart-section,
  .data-info {
    margin-left: 20rpx;
    margin-right: 20rpx;
  }
  
  .time-range-selector {
    margin-left: 20rpx;
    margin-right: 20rpx;
  }
  
  .assets-value {
    font-size: 42rpx;
  }
  
  .change-value {
    font-size: 28rpx;
  }
  
  .change-percent {
    font-size: 24rpx;
  }
}

/* 动画效果 */
.asset-overview,
.chart-wrapper,
.data-info {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.range-item {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}