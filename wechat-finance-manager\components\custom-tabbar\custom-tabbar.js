// components/custom-tabbar/custom-tabbar.js
Component({
  properties: {
    currentTab: {
      type: Number,
      value: 0
    },
    theme: {
      type: String,
      value: 'light'
    }
  },

  data: {
    switching: false
  },

  methods: {
    switchTab(e) {
      const { index, path } = e.currentTarget.dataset
      
      if (index === this.properties.currentTab) {
        return
      }

      // 添加切换动画
      this.setData({ switching: true })
      
      setTimeout(() => {
        this.setData({ switching: false })
      }, 200)

      // 触发页面切换事件
      this.triggerEvent('tabchange', {
        index: parseInt(index),
        path: path
      })

      // 执行页面跳转
      this.navigateToTab(path)
    },

    navigateToTab(path) {
      // 使用 switchTab 进行页面切换，提供平滑过渡
      wx.switchTab({
        url: path,
        success: () => {
          // 切换成功后的回调
          this.triggerEvent('tabswitched', {
            path: path
          })
        },
        fail: (error) => {
          console.error('Tab switch failed:', error)
          // 如果 switchTab 失败，尝试使用 navigateTo
          wx.navigateTo({
            url: path,
            fail: (navError) => {
              console.error('Navigation failed:', navError)
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              })
            }
          })
        }
      })
    },

    // 获取当前页面对应的 tab 索引
    getCurrentTabIndex(pagePath) {
      const tabPaths = [
        '/pages/index/index',
        '/pages/accounts/accounts',
        '/pages/transactions/transactions',
        '/pages/funds/funds',
        '/pages/settings/settings'
      ]
      
      return tabPaths.findIndex(path => pagePath.includes(path.split('/').pop()))
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化时获取当前页面路径
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const currentIndex = this.getCurrentTabIndex(currentPage.route)
        if (currentIndex !== -1) {
          this.setData({
            currentTab: currentIndex
          })
        }
      }
    }
  }
})