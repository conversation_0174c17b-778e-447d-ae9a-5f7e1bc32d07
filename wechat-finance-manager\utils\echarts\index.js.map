{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/index.js", "webpack:///./src/wx-canvas.js"], "names": ["ctx", "compareVersion", "v1", "v2", "split", "len", "Math", "max", "length", "push", "i", "num1", "parseInt", "num2", "Component", "properties", "canvasId", "type", "String", "value", "echarts", "Object", "ec", "forceUseOldCanvas", "Boolean", "data", "isUseNewCanvas", "ready", "console", "warn", "registerPreprocessor", "option", "series", "for<PERSON>ach", "progressive", "lazyLoad", "init", "methods", "callback", "version", "wx", "getSystemInfoSync", "SDKVersion", "canUseNewCanvas", "setData", "initByNewWay", "<PERSON><PERSON><PERSON><PERSON>", "error", "initByOldWay", "createCanvasContext", "canvas", "WxCanvas", "setCanvasCreator", "canvasDpr", "query", "createSelectorQuery", "in", "select", "boundingClientRect", "chart", "res", "width", "height", "onInit", "triggerEvent", "exec", "fields", "node", "size", "canvasNode", "pixelRatio", "canvasWidth", "canvasHeight", "getContext", "dpr", "canvasToTempFilePath", "opt", "draw", "touchStart", "e", "touches", "touch", "handler", "getZr", "dispatch", "zrX", "x", "zrY", "y", "processGesture", "wrapTouch", "touchMove", "touchEnd", "changedTouches", "event", "offsetX", "offsetY", "isNew", "_initStyle", "_initEvent", "contextType", "<PERSON><PERSON><PERSON>", "attachEvent", "detachEvent", "_initCanvas", "zrender", "util", "$override", "text", "font", "measureText", "styles", "defineProperty", "style", "set", "char<PERSON>t", "toUpperCase", "slice", "createRadialGradient", "createCircularGradient", "arguments", "eventNames", "wxName", "ecName", "name", "clientX", "clientY", "w", "h"], "mappings": ";;QAAA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;AClFA;;;;;;AAEA,IAAIA,YAAJ;;AAEA,SAASC,cAAT,CAAwBC,EAAxB,EAA4BC,EAA5B,EAAgC;AAC9BD,OAAKA,GAAGE,KAAH,CAAS,GAAT,CAAL;AACAD,OAAKA,GAAGC,KAAH,CAAS,GAAT,CAAL;AACA,MAAMC,MAAMC,KAAKC,GAAL,CAASL,GAAGM,MAAZ,EAAoBL,GAAGK,MAAvB,CAAZ;;AAEA,SAAON,GAAGM,MAAH,GAAYH,GAAnB,EAAwB;AACtBH,OAAGO,IAAH,CAAQ,GAAR;AACD;AACD,SAAON,GAAGK,MAAH,GAAYH,GAAnB,EAAwB;AACtBF,OAAGM,IAAH,CAAQ,GAAR;AACD;;AAED,OAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIL,GAApB,EAAyBK,GAAzB,EAA8B;AAC5B,QAAMC,OAAOC,SAASV,GAAGQ,CAAH,CAAT,CAAb;AACA,QAAMG,OAAOD,SAAST,GAAGO,CAAH,CAAT,CAAb;;AAEA,QAAIC,OAAOE,IAAX,EAAiB;AACf,aAAO,CAAP;AACD,KAFD,MAEO,IAAIF,OAAOE,IAAX,EAAiB;AACtB,aAAO,CAAC,CAAR;AACD;AACF;AACD,SAAO,CAAP;AACD;;AAEDC,UAAU;AACRC,cAAY;AACVC,cAAU;AACRC,YAAMC,MADE;AAERC,aAAO;AAFC,KADA;;AAMVC,aAAS;AACPH,YAAMI;AADC,KANC;;AAUVC,QAAI;AACFL,YAAMI;AADJ,KAVM;;AAcVE,uBAAmB;AACjBN,YAAMO,OADW;AAEjBL,aAAO;AAFU;AAdT,GADJ;;AAqBRM,QAAM;AACJC,oBAAgB;AADZ,GArBE;;AAyBRC,SAAO,iBAAY;AACjB,QAAI,CAAC,KAAKF,IAAL,CAAUL,OAAf,EAAwB;AACtBQ,cAAQC,IAAR,CAAa,gBAAb;AACA;AACD;;AAED;AACA;AACA,SAAKJ,IAAL,CAAUL,OAAV,CAAkBU,oBAAlB,CAAuC,kBAAU;AAC/C,UAAIC,UAAUA,OAAOC,MAArB,EAA6B;AAC3B,YAAID,OAAOC,MAAP,CAAcxB,MAAd,GAAuB,CAA3B,EAA8B;AAC5BuB,iBAAOC,MAAP,CAAcC,OAAd,CAAsB,kBAAU;AAC9BD,mBAAOE,WAAP,GAAqB,CAArB;AACD,WAFD;AAGD,SAJD,MAKK,IAAI,QAAOH,OAAOC,MAAd,MAAyB,QAA7B,EAAuC;AAC1CD,iBAAOC,MAAP,CAAcE,WAAd,GAA4B,CAA5B;AACD;AACF;AACF,KAXD;;AAaA,QAAI,CAAC,KAAKT,IAAL,CAAUH,EAAf,EAAmB;AACjBM,cAAQC,IAAR,CAAa,mDACT,oDADJ;AAEA;AACD;;AAED,QAAI,CAAC,KAAKJ,IAAL,CAAUH,EAAV,CAAaa,QAAlB,EAA4B;AAC1B,WAAKC,IAAL;AACD;AACF,GAvDO;;AAyDRC,WAAS;AACPD,UAAM,cAAUE,QAAV,EAAoB;AACxB,UAAMC,UAAUC,GAAGC,iBAAH,GAAuBC,UAAvC;;AAEA,UAAMC,kBAAkB1C,eAAesC,OAAf,EAAwB,OAAxB,KAAoC,CAA5D;AACA,UAAMhB,oBAAoB,KAAKE,IAAL,CAAUF,iBAApC;AACA,UAAMG,iBAAiBiB,mBAAmB,CAACpB,iBAA3C;AACA,WAAKqB,OAAL,CAAa,EAAElB,8BAAF,EAAb;;AAEA,UAAIH,qBAAqBoB,eAAzB,EAA0C;AACxCf,gBAAQC,IAAR,CAAa,qBAAb;AACD;;AAED,UAAIH,cAAJ,EAAoB;AAClB;AACA;AACA,aAAKmB,YAAL,CAAkBP,QAAlB;AACD,OAJD,MAIO;AACL,YAAMQ,UAAU7C,eAAesC,OAAf,EAAwB,QAAxB,KAAqC,CAArD;AACA,YAAI,CAACO,OAAL,EAAc;AACZlB,kBAAQmB,KAAR,CAAc,4BACV,iDADU,GAEV,yDAFJ;AAGA;AACD,SALD,MAKO;AACLnB,kBAAQC,IAAR,CAAa,mCAAb;AACA,eAAKmB,YAAL,CAAkBV,QAAlB;AACD;AACF;AACF,KA7BM;;AA+BPU,gBA/BO,wBA+BMV,QA/BN,EA+BgB;AAAA;;AACrB;AACAtC,YAAMwC,GAAGS,mBAAH,CAAuB,KAAKxB,IAAL,CAAUT,QAAjC,EAA2C,IAA3C,CAAN;AACA,UAAMkC,SAAS,IAAIC,kBAAJ,CAAanD,GAAb,EAAkB,KAAKyB,IAAL,CAAUT,QAA5B,EAAsC,KAAtC,CAAf;;AAEA,WAAKS,IAAL,CAAUL,OAAV,CAAkBgC,gBAAlB,CAAmC,YAAM;AACvC,eAAOF,MAAP;AACD,OAFD;AAGA;AACA,UAAMG,YAAY,CAAlB;AACA,UAAIC,QAAQd,GAAGe,mBAAH,GAAyBC,EAAzB,CAA4B,IAA5B,CAAZ;AACAF,YAAMG,MAAN,CAAa,YAAb,EAA2BC,kBAA3B,CAA8C,eAAO;AACnD,YAAI,OAAOpB,QAAP,KAAoB,UAAxB,EAAoC;AAClC,gBAAKqB,KAAL,GAAarB,SAASY,MAAT,EAAiBU,IAAIC,KAArB,EAA4BD,IAAIE,MAAhC,EAAwCT,SAAxC,CAAb;AACD,SAFD,MAGK,IAAI,MAAK5B,IAAL,CAAUH,EAAV,IAAgB,OAAO,MAAKG,IAAL,CAAUH,EAAV,CAAayC,MAApB,KAA+B,UAAnD,EAA+D;AAClE,gBAAKJ,KAAL,GAAa,MAAKlC,IAAL,CAAUH,EAAV,CAAayC,MAAb,CAAoBb,MAApB,EAA4BU,IAAIC,KAAhC,EAAuCD,IAAIE,MAA3C,EAAmDT,SAAnD,CAAb;AACD,SAFI,MAGA;AACH,gBAAKW,YAAL,CAAkB,MAAlB,EAA0B;AACxBd,oBAAQA,MADgB;AAExBW,mBAAOD,IAAIC,KAFa;AAGxBC,oBAAQF,IAAIE,MAHY;AAIxBT,uBAAWA,SAJa,CAIH;AAJG,WAA1B;AAMD;AACF,OAfD,EAeGY,IAfH;AAgBD,KA1DM;AA4DPpB,gBA5DO,wBA4DMP,QA5DN,EA4DgB;AAAA;;AACrB;AACA,UAAMgB,QAAQd,GAAGe,mBAAH,GAAyBC,EAAzB,CAA4B,IAA5B,CAAd;AACAF,YACGG,MADH,CACU,YADV,EAEGS,MAFH,CAEU,EAAEC,MAAM,IAAR,EAAcC,MAAM,IAApB,EAFV,EAGGH,IAHH,CAGQ,eAAO;AACX,YAAMI,aAAaT,IAAI,CAAJ,EAAOO,IAA1B;AACA,eAAKE,UAAL,GAAkBA,UAAlB;;AAEA,YAAMhB,YAAYb,GAAGC,iBAAH,GAAuB6B,UAAzC;AACA,YAAMC,cAAcX,IAAI,CAAJ,EAAOC,KAA3B;AACA,YAAMW,eAAeZ,IAAI,CAAJ,EAAOE,MAA5B;;AAEA,YAAM9D,MAAMqE,WAAWI,UAAX,CAAsB,IAAtB,CAAZ;;AAEA,YAAMvB,SAAS,IAAIC,kBAAJ,CAAanD,GAAb,EAAkB,OAAKyB,IAAL,CAAUT,QAA5B,EAAsC,IAAtC,EAA4CqD,UAA5C,CAAf;AACA,eAAK5C,IAAL,CAAUL,OAAV,CAAkBgC,gBAAlB,CAAmC,YAAM;AACvC,iBAAOF,MAAP;AACD,SAFD;;AAIA,YAAI,OAAOZ,QAAP,KAAoB,UAAxB,EAAoC;AAClC,iBAAKqB,KAAL,GAAarB,SAASY,MAAT,EAAiBqB,WAAjB,EAA8BC,YAA9B,EAA4CnB,SAA5C,CAAb;AACD,SAFD,MAEO,IAAI,OAAK5B,IAAL,CAAUH,EAAV,IAAgB,OAAO,OAAKG,IAAL,CAAUH,EAAV,CAAayC,MAApB,KAA+B,UAAnD,EAA+D;AACpE,iBAAKJ,KAAL,GAAa,OAAKlC,IAAL,CAAUH,EAAV,CAAayC,MAAb,CAAoBb,MAApB,EAA4BqB,WAA5B,EAAyCC,YAAzC,EAAuDnB,SAAvD,CAAb;AACD,SAFM,MAEA;AACL,iBAAKW,YAAL,CAAkB,MAAlB,EAA0B;AACxBd,oBAAQA,MADgB;AAExBW,mBAAOU,WAFiB;AAGxBT,oBAAQU,YAHgB;AAIxBE,iBAAKrB;AAJmB,WAA1B;AAMD;AACF,OA9BH;AA+BD,KA9FM;AA+FPsB,wBA/FO,gCA+FcC,GA/Fd,EA+FmB;AAAA;;AACxB,UAAI,KAAKnD,IAAL,CAAUC,cAAd,EAA8B;AAC5B;AACA,YAAM4B,QAAQd,GAAGe,mBAAH,GAAyBC,EAAzB,CAA4B,IAA5B,CAAd;AACAF,cACGG,MADH,CACU,YADV,EAEGS,MAFH,CAEU,EAAEC,MAAM,IAAR,EAAcC,MAAM,IAApB,EAFV,EAGGH,IAHH,CAGQ,eAAO;AACX,cAAMI,aAAaT,IAAI,CAAJ,EAAOO,IAA1B;AACAS,cAAI1B,MAAJ,GAAamB,UAAb;AACA7B,aAAGmC,oBAAH,CAAwBC,GAAxB;AACD,SAPH;AAQD,OAXD,MAWO;AACL;AACA,YAAI,CAACA,IAAI5D,QAAT,EAAmB;AACjB4D,cAAI5D,QAAJ,GAAe,KAAKS,IAAL,CAAUT,QAAzB;AACD;AACDhB,YAAI6E,IAAJ,CAAS,IAAT,EAAe,YAAM;AACnBrC,aAAGmC,oBAAH,CAAwBC,GAAxB,EAA6B,MAA7B;AACD,SAFD;AAGD;AACF,KApHM;AAsHPE,cAtHO,sBAsHIC,CAtHJ,EAsHO;AACZ,UAAI,KAAKpB,KAAL,IAAcoB,EAAEC,OAAF,CAAUxE,MAAV,GAAmB,CAArC,EAAwC;AACtC,YAAIyE,QAAQF,EAAEC,OAAF,CAAU,CAAV,CAAZ;AACA,YAAIE,UAAU,KAAKvB,KAAL,CAAWwB,KAAX,GAAmBD,OAAjC;AACAA,gBAAQE,QAAR,CAAiB,WAAjB,EAA8B;AAC5BC,eAAKJ,MAAMK,CADiB;AAE5BC,eAAKN,MAAMO;AAFiB,SAA9B;AAIAN,gBAAQE,QAAR,CAAiB,WAAjB,EAA8B;AAC5BC,eAAKJ,MAAMK,CADiB;AAE5BC,eAAKN,MAAMO;AAFiB,SAA9B;AAIAN,gBAAQO,cAAR,CAAuBC,UAAUX,CAAV,CAAvB,EAAqC,OAArC;AACD;AACF,KApIM;AAsIPY,aAtIO,qBAsIGZ,CAtIH,EAsIM;AACX,UAAI,KAAKpB,KAAL,IAAcoB,EAAEC,OAAF,CAAUxE,MAAV,GAAmB,CAArC,EAAwC;AACtC,YAAIyE,QAAQF,EAAEC,OAAF,CAAU,CAAV,CAAZ;AACA,YAAIE,UAAU,KAAKvB,KAAL,CAAWwB,KAAX,GAAmBD,OAAjC;AACAA,gBAAQE,QAAR,CAAiB,WAAjB,EAA8B;AAC5BC,eAAKJ,MAAMK,CADiB;AAE5BC,eAAKN,MAAMO;AAFiB,SAA9B;AAIAN,gBAAQO,cAAR,CAAuBC,UAAUX,CAAV,CAAvB,EAAqC,QAArC;AACD;AACF,KAhJM;AAkJPa,YAlJO,oBAkJEb,CAlJF,EAkJK;AACV,UAAI,KAAKpB,KAAT,EAAgB;AACd,YAAMsB,QAAQF,EAAEc,cAAF,GAAmBd,EAAEc,cAAF,CAAiB,CAAjB,CAAnB,GAAyC,EAAvD;AACA,YAAIX,UAAU,KAAKvB,KAAL,CAAWwB,KAAX,GAAmBD,OAAjC;AACAA,gBAAQE,QAAR,CAAiB,SAAjB,EAA4B;AAC1BC,eAAKJ,MAAMK,CADe;AAE1BC,eAAKN,MAAMO;AAFe,SAA5B;AAIAN,gBAAQE,QAAR,CAAiB,OAAjB,EAA0B;AACxBC,eAAKJ,MAAMK,CADa;AAExBC,eAAKN,MAAMO;AAFa,SAA1B;AAIAN,gBAAQO,cAAR,CAAuBC,UAAUX,CAAV,CAAvB,EAAqC,KAArC;AACD;AACF;AAhKM;AAzDD,CAAV;;AA6NA,SAASW,SAAT,CAAmBI,KAAnB,EAA0B;AACxB,OAAK,IAAIpF,IAAI,CAAb,EAAgBA,IAAIoF,MAAMd,OAAN,CAAcxE,MAAlC,EAA0C,EAAEE,CAA5C,EAA+C;AAC7C,QAAMuE,QAAQa,MAAMd,OAAN,CAActE,CAAd,CAAd;AACAuE,UAAMc,OAAN,GAAgBd,MAAMK,CAAtB;AACAL,UAAMe,OAAN,GAAgBf,MAAMO,CAAtB;AACD;AACD,SAAOM,KAAP;AACD,C;;;;;;;;;;;;;;;ICjQoB3C,Q;AACnB,oBAAYnD,GAAZ,EAAiBgB,QAAjB,EAA2BiF,KAA3B,EAAkC5B,UAAlC,EAA8C;AAAA;;AAC5C,SAAKrE,GAAL,GAAWA,GAAX;AACA,SAAKgB,QAAL,GAAgBA,QAAhB;AACA,SAAK2C,KAAL,GAAa,IAAb;AACA,SAAKsC,KAAL,GAAaA,KAAb;AACA,QAAIA,KAAJ,EAAW;AACT,WAAK5B,UAAL,GAAkBA,UAAlB;AACD,KAFD,MAGK;AACH,WAAK6B,UAAL,CAAgBlG,GAAhB;AACD;;AAED;;AAEA,SAAKmG,UAAL;AACD;;qBAED1B,U,uBAAW2B,W,EAAa;AACtB,QAAIA,gBAAgB,IAApB,EAA0B;AACxB,aAAO,KAAKpG,GAAZ;AACD;AACF,G;;AAED;AACA;AACA;AACA;AACA;AACA;;qBAEAqG,Q,qBAAS1C,K,EAAO;AACd,SAAKA,KAAL,GAAaA,KAAb;AACD,G;;qBAED2C,W,0BAAc;AACZ;AACD,G;;qBAEDC,W,0BAAc;AACZ;AACD,G;;qBAEDC,W,wBAAYC,O,EAASzG,G,EAAK;AACxByG,YAAQC,IAAR,CAAajC,UAAb,GAA0B,YAAY;AACpC,aAAOzE,GAAP;AACD,KAFD;;AAIAyG,YAAQC,IAAR,CAAaC,SAAb,CAAuB,aAAvB,EAAsC,UAAUC,IAAV,EAAgBC,IAAhB,EAAsB;AAC1D7G,UAAI6G,IAAJ,GAAWA,QAAQ,iBAAnB;AACA,aAAO7G,IAAI8G,WAAJ,CAAgBF,IAAhB,CAAP;AACD,KAHD;AAID,G;;qBAEDV,U,uBAAWlG,G,EAAK;AAAA;;AACd,QAAI+G,SAAS,CAAC,WAAD,EAAc,aAAd,EAA6B,aAA7B,EACX,WADW,EACE,eADF,EACmB,QADnB,EAC6B,WAD7B,EAEX,SAFW,EAEA,UAFA,EAEY,UAFZ,EAEwB,YAFxB,EAEsC,UAFtC,CAAb;;AAIAA,WAAO9E,OAAP,CAAe,iBAAS;AACtBZ,aAAO2F,cAAP,CAAsBhH,GAAtB,EAA2BiH,KAA3B,EAAkC;AAChCC,aAAK,oBAAS;AACZ,cAAID,UAAU,WAAV,IAAyBA,UAAU,aAAnC,IACC9F,UAAU,MAAV,IAAoBA,UAAU,IADnC,EAEE;AACAnB,gBAAI,QAAQiH,MAAME,MAAN,CAAa,CAAb,EAAgBC,WAAhB,EAAR,GAAwCH,MAAMI,KAAN,CAAY,CAAZ,CAA5C,EAA4DlG,KAA5D;AACD;AACF;AAP+B,OAAlC;AASD,KAVD;;AAYAnB,QAAIsH,oBAAJ,GAA2B,YAAM;AAC/B,aAAOtH,IAAIuH,sBAAJ,CAA2BC,UAA3B,CAAP;AACD,KAFD;AAGD,G;;qBAEDrB,U,yBAAa;AAAA;;AACX,SAAKL,KAAL,GAAa,EAAb;AACA,QAAM2B,aAAa,CAAC;AAClBC,cAAQ,YADU;AAElBC,cAAQ;AAFU,KAAD,EAGhB;AACDD,cAAQ,WADP;AAEDC,cAAQ;AAFP,KAHgB,EAMhB;AACDD,cAAQ,UADP;AAEDC,cAAQ;AAFP,KANgB,EAShB;AACDD,cAAQ,UADP;AAEDC,cAAQ;AAFP,KATgB,CAAnB;;AAcAF,eAAWxF,OAAX,CAAmB,gBAAQ;AACzB,YAAK6D,KAAL,CAAW8B,KAAKF,MAAhB,IAA0B,aAAK;AAC7B,YAAMzC,QAAQF,EAAEC,OAAF,CAAU,CAAV,CAAd;AACA,cAAKrB,KAAL,CAAWwB,KAAX,GAAmBD,OAAnB,CAA2BE,QAA3B,CAAoCwC,KAAKD,MAAzC,EAAiD;AAC/CtC,eAAKuC,KAAKF,MAAL,KAAgB,KAAhB,GAAwBzC,MAAM4C,OAA9B,GAAwC5C,MAAMK,CADJ;AAE/CC,eAAKqC,KAAKF,MAAL,KAAgB,KAAhB,GAAwBzC,MAAM6C,OAA9B,GAAwC7C,MAAMO;AAFJ,SAAjD;AAID,OAND;AAOD,KARD;AASD,G;;;;sBAESuC,C,EAAG;AACX,UAAI,KAAK1D,UAAT,EAAqB,KAAKA,UAAL,CAAgBR,KAAhB,GAAwBkE,CAAxB;AACtB,K;wBAKW;AACV,UAAI,KAAK1D,UAAT,EACE,OAAO,KAAKA,UAAL,CAAgBR,KAAvB;AACF,aAAO,CAAP;AACD;;;sBARUmE,C,EAAG;AACZ,UAAI,KAAK3D,UAAT,EAAqB,KAAKA,UAAL,CAAgBP,MAAhB,GAAyBkE,CAAzB;AACtB,K;wBAOY;AACX,UAAI,KAAK3D,UAAT,EACE,OAAO,KAAKA,UAAL,CAAgBP,MAAvB;AACF,aAAO,CAAP;AACD;;;;;;kBAvHkBX,Q", "file": "index.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "import WxCanvas from './wx-canvas';\n\nlet ctx;\n\nfunction compareVersion(v1, v2) {\n  v1 = v1.split('.')\n  v2 = v2.split('.')\n  const len = Math.max(v1.length, v2.length)\n\n  while (v1.length < len) {\n    v1.push('0')\n  }\n  while (v2.length < len) {\n    v2.push('0')\n  }\n\n  for (let i = 0; i < len; i++) {\n    const num1 = parseInt(v1[i])\n    const num2 = parseInt(v2[i])\n\n    if (num1 > num2) {\n      return 1\n    } else if (num1 < num2) {\n      return -1\n    }\n  }\n  return 0\n}\n\nComponent({\n  properties: {\n    canvasId: {\n      type: String,\n      value: 'ec-canvas'\n    },\n\n    echarts: {\n      type: Object\n    },\n\n    ec: {\n      type: Object\n    },\n\n    forceUseOldCanvas: {\n      type: Boolean,\n      value: false\n    }\n  },\n\n  data: {\n    isUseNewCanvas: false\n  },\n\n  ready: function () {\n    if (!this.data.echarts) {\n      console.warn('组件需要传入 echarts')\n      return;\n    }\n\n    // Disable prograssive because drawImage doesn't support DOM as parameter\n    // See https://developers.weixin.qq.com/miniprogram/dev/api/canvas/CanvasContext.drawImage.html\n    this.data.echarts.registerPreprocessor(option => {\n      if (option && option.series) {\n        if (option.series.length > 0) {\n          option.series.forEach(series => {\n            series.progressive = 0;\n          });\n        }\n        else if (typeof option.series === 'object') {\n          option.series.progressive = 0;\n        }\n      }\n    });\n\n    if (!this.data.ec) {\n      console.warn('组件需绑定 ec 变量，例：<ec-canvas id=\"mychart-dom-bar\" '\n        + 'canvas-id=\"mychart-bar\" ec=\"{{ ec }}\"></ec-canvas>');\n      return;\n    }\n\n    if (!this.data.ec.lazyLoad) {\n      this.init();\n    }\n  },\n\n  methods: {\n    init: function (callback) {\n      const version = wx.getSystemInfoSync().SDKVersion\n\n      const canUseNewCanvas = compareVersion(version, '2.9.0') >= 0;\n      const forceUseOldCanvas = this.data.forceUseOldCanvas;\n      const isUseNewCanvas = canUseNewCanvas && !forceUseOldCanvas;\n      this.setData({ isUseNewCanvas });\n\n      if (forceUseOldCanvas && canUseNewCanvas) {\n        console.warn('开发者强制使用旧canvas,建议关闭');\n      }\n\n      if (isUseNewCanvas) {\n        // console.log('微信基础库版本大于2.9.0，开始使用<canvas type=\"2d\"/>');\n        // 2.9.0 可以使用 <canvas type=\"2d\"></canvas>\n        this.initByNewWay(callback);\n      } else {\n        const isValid = compareVersion(version, '1.9.91') >= 0\n        if (!isValid) {\n          console.error('微信基础库版本过低，需大于等于 1.9.91。'\n            + '参见：https://github.com/ecomfe/echarts-for-weixin'\n            + '#%E5%BE%AE%E4%BF%A1%E7%89%88%E6%9C%AC%E8%A6%81%E6%B1%82');\n          return;\n        } else {\n          console.warn('建议将微信基础库调整大于等于2.9.0版本。升级后绘图将有更好性能');\n          this.initByOldWay(callback);\n        }\n      }\n    },\n\n    initByOldWay(callback) {\n      // 1.9.91 <= version < 2.9.0：原来的方式初始化\n      ctx = wx.createCanvasContext(this.data.canvasId, this);\n      const canvas = new WxCanvas(ctx, this.data.canvasId, false);\n\n      this.data.echarts.setCanvasCreator(() => {\n        return canvas;\n      });\n      // const canvasDpr = wx.getSystemInfoSync().pixelRatio // 微信旧的canvas不能传入dpr\n      const canvasDpr = 1\n      var query = wx.createSelectorQuery().in(this);\n      query.select('.ec-canvas').boundingClientRect(res => {\n        if (typeof callback === 'function') {\n          this.chart = callback(canvas, res.width, res.height, canvasDpr);\n        }\n        else if (this.data.ec && typeof this.data.ec.onInit === 'function') {\n          this.chart = this.data.ec.onInit(canvas, res.width, res.height, canvasDpr);\n        }\n        else {\n          this.triggerEvent('init', {\n            canvas: canvas,\n            width: res.width,\n            height: res.height,\n            canvasDpr: canvasDpr // 增加了dpr，可方便外面echarts.init\n          });\n        }\n      }).exec();\n    },\n\n    initByNewWay(callback) {\n      // version >= 2.9.0：使用新的方式初始化\n      const query = wx.createSelectorQuery().in(this)\n      query\n        .select('.ec-canvas')\n        .fields({ node: true, size: true })\n        .exec(res => {\n          const canvasNode = res[0].node\n          this.canvasNode = canvasNode\n\n          const canvasDpr = wx.getSystemInfoSync().pixelRatio\n          const canvasWidth = res[0].width\n          const canvasHeight = res[0].height\n\n          const ctx = canvasNode.getContext('2d')\n\n          const canvas = new WxCanvas(ctx, this.data.canvasId, true, canvasNode)\n          this.data.echarts.setCanvasCreator(() => {\n            return canvas\n          })\n\n          if (typeof callback === 'function') {\n            this.chart = callback(canvas, canvasWidth, canvasHeight, canvasDpr)\n          } else if (this.data.ec && typeof this.data.ec.onInit === 'function') {\n            this.chart = this.data.ec.onInit(canvas, canvasWidth, canvasHeight, canvasDpr)\n          } else {\n            this.triggerEvent('init', {\n              canvas: canvas,\n              width: canvasWidth,\n              height: canvasHeight,\n              dpr: canvasDpr\n            })\n          }\n        })\n    },\n    canvasToTempFilePath(opt) {\n      if (this.data.isUseNewCanvas) {\n        // 新版\n        const query = wx.createSelectorQuery().in(this)\n        query\n          .select('.ec-canvas')\n          .fields({ node: true, size: true })\n          .exec(res => {\n            const canvasNode = res[0].node\n            opt.canvas = canvasNode\n            wx.canvasToTempFilePath(opt)\n          })\n      } else {\n        // 旧的\n        if (!opt.canvasId) {\n          opt.canvasId = this.data.canvasId;\n        }\n        ctx.draw(true, () => {\n          wx.canvasToTempFilePath(opt, this);\n        });\n      }\n    },\n\n    touchStart(e) {\n      if (this.chart && e.touches.length > 0) {\n        var touch = e.touches[0];\n        var handler = this.chart.getZr().handler;\n        handler.dispatch('mousedown', {\n          zrX: touch.x,\n          zrY: touch.y\n        });\n        handler.dispatch('mousemove', {\n          zrX: touch.x,\n          zrY: touch.y\n        });\n        handler.processGesture(wrapTouch(e), 'start');\n      }\n    },\n\n    touchMove(e) {\n      if (this.chart && e.touches.length > 0) {\n        var touch = e.touches[0];\n        var handler = this.chart.getZr().handler;\n        handler.dispatch('mousemove', {\n          zrX: touch.x,\n          zrY: touch.y\n        });\n        handler.processGesture(wrapTouch(e), 'change');\n      }\n    },\n\n    touchEnd(e) {\n      if (this.chart) {\n        const touch = e.changedTouches ? e.changedTouches[0] : {};\n        var handler = this.chart.getZr().handler;\n        handler.dispatch('mouseup', {\n          zrX: touch.x,\n          zrY: touch.y\n        });\n        handler.dispatch('click', {\n          zrX: touch.x,\n          zrY: touch.y\n        });\n        handler.processGesture(wrapTouch(e), 'end');\n      }\n    }\n  }\n});\n\nfunction wrapTouch(event) {\n  for (let i = 0; i < event.touches.length; ++i) {\n    const touch = event.touches[i];\n    touch.offsetX = touch.x;\n    touch.offsetY = touch.y;\n  }\n  return event;\n}\n", "export default class WxCanvas {\n  constructor(ctx, canvasId, isNew, canvasNode) {\n    this.ctx = ctx;\n    this.canvasId = canvasId;\n    this.chart = null;\n    this.isNew = isNew\n    if (isNew) {\n      this.canvasNode = canvasNode;\n    }\n    else {\n      this._initStyle(ctx);\n    }\n\n    // this._initCanvas(zrender, ctx);\n\n    this._initEvent();\n  }\n\n  getContext(contextType) {\n    if (contextType === '2d') {\n      return this.ctx;\n    }\n  }\n\n  // canvasToTempFilePath(opt) {\n  //   if (!opt.canvasId) {\n  //     opt.canvasId = this.canvasId;\n  //   }\n  //   return wx.canvasToTempFilePath(opt, this);\n  // }\n\n  setChart(chart) {\n    this.chart = chart;\n  }\n\n  attachEvent() {\n    // noop\n  }\n\n  detachEvent() {\n    // noop\n  }\n\n  _initCanvas(zrender, ctx) {\n    zrender.util.getContext = function () {\n      return ctx;\n    };\n\n    zrender.util.$override('measureText', function (text, font) {\n      ctx.font = font || '12px sans-serif';\n      return ctx.measureText(text);\n    });\n  }\n\n  _initStyle(ctx) {\n    var styles = ['fillStyle', 'strokeStyle', 'globalAlpha',\n      'textAlign', 'textBaseAlign', 'shadow', 'lineWidth',\n      'lineCap', 'lineJoin', 'lineDash', 'miterLimit', 'fontSize'];\n\n    styles.forEach(style => {\n      Object.defineProperty(ctx, style, {\n        set: value => {\n          if (style !== 'fillStyle' && style !== 'strokeStyle'\n            || value !== 'none' && value !== null\n          ) {\n            ctx['set' + style.charAt(0).toUpperCase() + style.slice(1)](value);\n          }\n        }\n      });\n    });\n\n    ctx.createRadialGradient = () => {\n      return ctx.createCircularGradient(arguments);\n    };\n  }\n\n  _initEvent() {\n    this.event = {};\n    const eventNames = [{\n      wxName: 'touchStart',\n      ecName: 'mousedown'\n    }, {\n      wxName: 'touchMove',\n      ecName: 'mousemove'\n    }, {\n      wxName: 'touchEnd',\n      ecName: 'mouseup'\n    }, {\n      wxName: 'touchEnd',\n      ecName: 'click'\n    }];\n\n    eventNames.forEach(name => {\n      this.event[name.wxName] = e => {\n        const touch = e.touches[0];\n        this.chart.getZr().handler.dispatch(name.ecName, {\n          zrX: name.wxName === 'tap' ? touch.clientX : touch.x,\n          zrY: name.wxName === 'tap' ? touch.clientY : touch.y\n        });\n      };\n    });\n  }\n\n  set width(w) {\n    if (this.canvasNode) this.canvasNode.width = w\n  }\n  set height(h) {\n    if (this.canvasNode) this.canvasNode.height = h\n  }\n\n  get width() {\n    if (this.canvasNode)\n      return this.canvasNode.width\n    return 0\n  }\n  get height() {\n    if (this.canvasNode)\n      return this.canvasNode.height\n    return 0\n  }\n}\n"], "sourceRoot": ""}