/**
 * 用户数据模型
 * 负责用户信息的数据结构定义、验证和数据库操作
 */

class User {
  constructor(data = {}) {
    this._id = data._id || null;
    this.openid = data.openid || '';
    this.nickName = data.nickName || '';
    this.avatarUrl = data.avatarUrl || '';
    this.theme = data.theme || 'light'; // light/dark
    this.settings = {
      autoRefresh: data.settings?.autoRefresh ?? true,
      notifications: data.settings?.notifications ?? true,
      defaultTimeRange: data.settings?.defaultTimeRange || 'month'
    };
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
  }

  /**
   * 验证用户数据
   * @returns {Object} 验证结果 {isValid: boolean, errors: Array}
   */
  validate() {
    const errors = [];

    // 验证openid
    if (!this.openid || typeof this.openid !== 'string') {
      errors.push('openid是必填字段且必须为字符串');
    }

    // 验证昵称
    if (this.nickName && typeof this.nickName !== 'string') {
      errors.push('昵称必须为字符串');
    }

    if (this.nickName && this.nickName.length > 50) {
      errors.push('昵称长度不能超过50个字符');
    }

    // 验证头像URL
    if (this.avatarUrl && typeof this.avatarUrl !== 'string') {
      errors.push('头像URL必须为字符串');
    }

    // 验证主题
    if (!['light', 'dark'].includes(this.theme)) {
      errors.push('主题只能是light或dark');
    }

    // 验证设置
    if (typeof this.settings.autoRefresh !== 'boolean') {
      errors.push('自动刷新设置必须为布尔值');
    }

    if (typeof this.settings.notifications !== 'boolean') {
      errors.push('通知设置必须为布尔值');
    }

    const validTimeRanges = ['week', 'month', 'quarter', 'half-year', 'year', 'all'];
    if (!validTimeRanges.includes(this.settings.defaultTimeRange)) {
      errors.push('默认时间范围设置无效');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 转换为数据库存储格式
   * @returns {Object} 数据库存储对象
   */
  toDatabase() {
    const data = {
      openid: this.openid,
      nickName: this.nickName,
      avatarUrl: this.avatarUrl,
      theme: this.theme,
      settings: this.settings,
      updatedAt: new Date()
    };

    // 如果是新用户，添加创建时间
    if (!this._id) {
      data.createdAt = new Date();
    }

    return data;
  }

  /**
   * 从数据库数据创建用户实例
   * @param {Object} dbData 数据库数据
   * @returns {User} 用户实例
   */
  static fromDatabase(dbData) {
    return new User({
      _id: dbData._id,
      openid: dbData.openid,
      nickName: dbData.nickName,
      avatarUrl: dbData.avatarUrl,
      theme: dbData.theme,
      settings: dbData.settings,
      createdAt: dbData.createdAt,
      updatedAt: dbData.updatedAt
    });
  }

  /**
   * 更新用户信息
   * @param {Object} updateData 更新数据
   */
  update(updateData) {
    if (updateData.nickName !== undefined) {
      this.nickName = updateData.nickName;
    }
    if (updateData.avatarUrl !== undefined) {
      this.avatarUrl = updateData.avatarUrl;
    }
    if (updateData.theme !== undefined) {
      this.theme = updateData.theme;
    }
    if (updateData.settings !== undefined) {
      this.settings = { ...this.settings, ...updateData.settings };
    }
    this.updatedAt = new Date();
  }

  /**
   * 获取用户显示信息
   * @returns {Object} 用户显示信息
   */
  getDisplayInfo() {
    return {
      _id: this._id,
      nickName: this.nickName || '未设置昵称',
      avatarUrl: this.avatarUrl,
      theme: this.theme,
      settings: this.settings
    };
  }
}

module.exports = User;