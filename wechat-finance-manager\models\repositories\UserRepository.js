/**
 * 用户数据仓库
 * 负责用户数据的数据库操作
 */

const User = require('../User');
const { COLLECTION_NAME } = require('../collections/users');

class UserRepository {
  constructor(db) {
    this.db = db;
    this.collection = db.collection(COLLECTION_NAME);
  }

  /**
   * 创建新用户
   * @param {User} user 用户实例
   * @returns {Promise<Object>} 创建结果
   */
  async create(user) {
    try {
      // 验证用户数据
      const validation = user.validate();
      if (!validation.isValid) {
        throw new Error(`用户数据验证失败: ${validation.errors.join(', ')}`);
      }

      // 检查用户是否已存在
      const existingUser = await this.findByOpenid(user.openid);
      if (existingUser) {
        throw new Error('用户已存在');
      }

      // 保存到数据库
      const result = await this.collection.add({
        data: user.toDatabase()
      });

      return {
        success: true,
        _id: result._id,
        message: '用户创建成功'
      };
    } catch (error) {
      console.error('创建用户失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 根据openid查找用户
   * @param {string} openid 微信openid
   * @returns {Promise<User|null>} 用户实例或null
   */
  async findByOpenid(openid) {
    try {
      const result = await this.collection.where({
        openid: openid
      }).get();

      if (result.data.length === 0) {
        return null;
      }

      return User.fromDatabase(result.data[0]);
    } catch (error) {
      console.error('查找用户失败:', error);
      return null;
    }
  }

  /**
   * 根据ID查找用户
   * @param {string} userId 用户ID
   * @returns {Promise<User|null>} 用户实例或null
   */
  async findById(userId) {
    try {
      const result = await this.collection.doc(userId).get();

      if (!result.data) {
        return null;
      }

      return User.fromDatabase(result.data);
    } catch (error) {
      console.error('查找用户失败:', error);
      return null;
    }
  }

  /**
   * 更新用户信息
   * @param {string} userId 用户ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  async update(userId, updateData) {
    try {
      // 先获取现有用户数据
      const existingUser = await this.findById(userId);
      if (!existingUser) {
        throw new Error('用户不存在');
      }

      // 更新用户数据
      existingUser.update(updateData);

      // 验证更新后的数据
      const validation = existingUser.validate();
      if (!validation.isValid) {
        throw new Error(`用户数据验证失败: ${validation.errors.join(', ')}`);
      }

      // 保存到数据库
      await this.collection.doc(userId).update({
        data: existingUser.toDatabase()
      });

      return {
        success: true,
        message: '用户信息更新成功'
      };
    } catch (error) {
      console.error('更新用户失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 删除用户
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 删除结果
   */
  async delete(userId) {
    try {
      // 检查用户是否存在
      const existingUser = await this.findById(userId);
      if (!existingUser) {
        throw new Error('用户不存在');
      }

      // 删除用户数据
      await this.collection.doc(userId).remove();

      return {
        success: true,
        message: '用户删除成功'
      };
    } catch (error) {
      console.error('删除用户失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取用户列表（管理员功能）
   * @param {Object} options 查询选项
   * @returns {Promise<Array>} 用户列表
   */
  async findAll(options = {}) {
    try {
      const { limit = 20, skip = 0, orderBy = 'createdAt', order = 'desc' } = options;

      let query = this.collection;

      // 添加排序
      if (order === 'desc') {
        query = query.orderBy(orderBy, 'desc');
      } else {
        query = query.orderBy(orderBy, 'asc');
      }

      // 添加分页
      query = query.skip(skip).limit(limit);

      const result = await query.get();

      return result.data.map(userData => User.fromDatabase(userData));
    } catch (error) {
      console.error('获取用户列表失败:', error);
      return [];
    }
  }

  /**
   * 统计用户数量
   * @returns {Promise<number>} 用户总数
   */
  async count() {
    try {
      const result = await this.collection.count();
      return result.total;
    } catch (error) {
      console.error('统计用户数量失败:', error);
      return 0;
    }
  }

  /**
   * 根据openid创建或更新用户
   * @param {Object} userData 用户数据
   * @returns {Promise<User>} 用户实例
   */
  async createOrUpdate(userData) {
    try {
      let user = await this.findByOpenid(userData.openid);

      if (user) {
        // 用户已存在，更新信息
        const updateResult = await this.update(user._id, userData);
        if (!updateResult.success) {
          throw new Error(updateResult.error);
        }
        // 重新获取更新后的用户数据
        user = await this.findById(user._id);
      } else {
        // 用户不存在，创建新用户
        const newUser = new User(userData);
        const createResult = await this.create(newUser);
        if (!createResult.success) {
          throw new Error(createResult.error);
        }
        user = await this.findById(createResult._id);
      }

      return user;
    } catch (error) {
      console.error('创建或更新用户失败:', error);
      throw error;
    }
  }
}

module.exports = UserRepository;