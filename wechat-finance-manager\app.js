// app.js
const ThemeService = require('./services/ThemeService')

App({
  onLaunch() {
    // 初始化云开发环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        // env 参数说明：
        //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
        //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
        //   如不填则使用默认环境（第一个创建的环境）
        env: 'finance-manager-cloud',
        traceUser: true,
      })
    }

    // 初始化主题服务
    this.initThemeService()

    // 检查用户登录状态
    this.checkLoginStatus()
  },

  onShow() {
    // 应用显示时的处理
    console.log('App Show')
  },

  onHide() {
    // 应用隐藏时的处理
    console.log('App Hide')
  },

  onError(msg) {
    // 应用错误处理
    console.error('App Error:', msg)
  },

  // 初始化主题服务
  initThemeService() {
    try {
      // 初始化主题
      const currentTheme = ThemeService.getCurrentTheme()
      this.globalData.theme = currentTheme

      // 监听系统主题变化
      ThemeService.onSystemThemeChange((theme) => {
        this.globalData.theme = theme
        console.log('System theme changed to:', theme)
      })

      // 添加主题变化监听器
      ThemeService.addThemeListener((theme) => {
        this.globalData.theme = theme
      })

    } catch (error) {
      console.error('Failed to initialize theme service:', error)
    }
  },

  // 检查用户登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    theme: 'light', // light | dark
    version: '1.0.0'
  }
})