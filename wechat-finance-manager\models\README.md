# 数据模型和云数据库设计

本文档描述了微信小程序理财管家应用的数据模型设计和实现。

## 概述

数据模型层包含三个核心模型：用户(User)、账户(Account)和持仓(Position)，每个模型都包含完整的数据验证、数据库操作和业务逻辑。

## 模型结构

### 1. 用户模型 (User)

**文件位置**: `models/User.js`

**主要功能**:
- 用户基本信息管理
- 主题偏好设置
- 应用配置管理
- 数据验证和权限控制

**核心字段**:
- `openid`: 微信用户唯一标识
- `nickName`: 用户昵称
- `avatarUrl`: 头像URL
- `theme`: 主题模式 (light/dark)
- `settings`: 应用设置对象

**数据库集合**: `users`
**索引配置**: `models/collections/users.js`
**数据仓库**: `models/repositories/UserRepository.js`

### 2. 账户模型 (Account)

**文件位置**: `models/Account.js`

**主要功能**:
- 证券账户和基金账户管理
- 账户资产价值计算
- 持仓列表管理
- 账户权限控制

**核心字段**:
- `userId`: 所属用户ID
- `brokerName`: 券商名称
- `accountNumber`: 账户号码
- `accountType`: 账户类型 (securities/fund)
- `totalValue`: 总资产价值
- `dailyChange`: 日变化金额
- `positions`: 持仓列表

**数据库集合**: `accounts`
**索引配置**: `models/collections/accounts.js`
**数据仓库**: `models/repositories/AccountRepository.js`

### 3. 持仓模型 (Position)

**文件位置**: `models/Position.js`

**主要功能**:
- 证券持仓数据管理
- 盈亏计算和统计
- 价格更新和同步
- 持仓操作管理

**核心字段**:
- `accountId`: 所属账户ID
- `userId`: 所属用户ID
- `symbol`: 证券代码
- `name`: 证券名称
- `type`: 证券类型 (stock/fund/bond)
- `quantity`: 持仓数量
- `costPrice`: 成本价
- `currentPrice`: 当前价格
- `marketValue`: 市值
- `unrealizedPnL`: 未实现盈亏

**数据库集合**: `positions`
**索引配置**: `models/collections/positions.js`
**数据仓库**: `models/repositories/PositionRepository.js`

## 数据库设计

### 集合结构

1. **users集合**
   - 存储用户基本信息和设置
   - 唯一索引：openid
   - 时间索引：createdAt, updatedAt

2. **accounts集合**
   - 存储账户信息
   - 唯一索引：accountNumber + brokerName
   - 复合索引：userId + accountType + isActive

3. **positions集合**
   - 存储持仓数据
   - 唯一索引：accountId + symbol
   - 复合索引：userId + type + isActive

### 权限规则

所有集合都实现了基于用户的权限控制：
```javascript
{
  read: "auth.openid == resource.data.userId",
  write: "auth.openid == resource.data.userId",
  create: "auth.openid == resource.data.userId",
  delete: "auth.openid == resource.data.userId"
}
```

## 数据仓库模式

每个模型都有对应的Repository类，提供标准的CRUD操作：

### 通用方法
- `create(model)`: 创建新记录
- `findById(id)`: 根据ID查找
- `update(id, data, userId)`: 更新记录
- `delete(id, userId)`: 删除记录

### 特定方法
- **UserRepository**: `findByOpenid()`, `createOrUpdate()`
- **AccountRepository**: `findByUserId()`, `updateAssetValue()`, `getUserAssetSummary()`
- **PositionRepository**: `findByAccountId()`, `batchUpdatePrices()`, `getAccountPositionSummary()`

## 数据验证

每个模型都实现了完整的数据验证：

### 验证规则
- 必填字段检查
- 数据类型验证
- 格式验证（如证券代码格式）
- 业务逻辑验证（如数量关系）
- 长度限制检查

### 验证方法
```javascript
const validation = model.validate();
if (!validation.isValid) {
  console.error('验证失败:', validation.errors);
}
```

## 业务逻辑

### 计算功能
- **持仓盈亏计算**: 自动计算未实现盈亏和盈亏百分比
- **账户资产统计**: 汇总账户总资产和日变化
- **持仓权重计算**: 计算单个持仓在账户中的占比

### 数据同步
- **价格更新**: 批量更新证券价格和相关计算
- **资产重算**: 价格变化时自动重新计算资产价值
- **状态管理**: 持仓冻结/解冻操作

## 安全特性

### 数据加密
- 敏感数据加密存储
- 账户号码掩码显示
- 权限验证机制

### 访问控制
- 用户级别的数据隔离
- 操作权限验证
- 数据库安全规则

## 测试覆盖

每个模型都有完整的单元测试：
- **User.test.js**: 用户模型测试 (12个测试用例)
- **Account.test.js**: 账户模型测试 (19个测试用例)
- **Position.test.js**: 持仓模型测试 (32个测试用例)

### 测试运行
```bash
npm test -- --testPathPattern="models"
```

## 初始化

使用统一的初始化函数设置数据库：

```javascript
const { initializeAllCollections, createRepositories } = require('./models');

// 初始化数据库集合
await initializeAllCollections(db);

// 创建数据仓库实例
const repositories = createRepositories(db);
```

## 使用示例

```javascript
const { User, Account, Position, createRepositories } = require('./models');

// 创建用户
const user = new User({
  openid: 'wx_openid_123',
  nickName: '投资者',
  theme: 'dark'
});

// 创建账户
const account = new Account({
  userId: user._id,
  brokerName: '华泰证券',
  accountNumber: '****************',
  accountType: 'securities'
});

// 创建持仓
const position = new Position({
  accountId: account._id,
  userId: user._id,
  symbol: '000001',
  name: '平安银行',
  type: 'stock',
  quantity: 1000,
  costPrice: 15.50,
  currentPrice: 16.20
});

// 使用数据仓库操作
const repositories = createRepositories(db);
await repositories.userRepository.create(user);
await repositories.accountRepository.create(account);
await repositories.positionRepository.create(position);
```

## 扩展性

数据模型设计考虑了未来扩展需求：
- 支持多种证券类型（股票、基金、债券）
- 支持多种账户类型（证券、基金）
- 灵活的配置和设置机制
- 可扩展的验证规则
- 模块化的仓库模式

这个数据模型层为整个应用提供了坚实的数据基础，确保了数据的完整性、安全性和可维护性。