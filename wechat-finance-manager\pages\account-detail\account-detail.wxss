/* pages/account-detail/account-detail.wxss */
@import "../../styles/common.wxss";

.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.content {
  padding: 20rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 账户信息卡片 */
.account-info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.broker-info {
  flex: 1;
}

.broker-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.account-number {
  font-size: 28rpx;
  color: #666;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.account-type {
  margin-left: 20rpx;
}

.type-badge {
  background: #007AFF;
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.account-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.stat-percent {
  font-size: 24rpx;
}

.stat-value.positive,
.stat-percent.positive {
  color: #34C759;
}

.stat-value.negative,
.stat-percent.negative {
  color: #FF3B30;
}

.stat-value.neutral,
.stat-percent.neutral {
  color: #666;
}

/* 持仓列表 */
.positions-section {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f2f2f7;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.position-count {
  font-size: 26rpx;
  color: #666;
  margin-left: 8rpx;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-btn-small {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
}

.action-btn-small:active {
  background: rgba(0, 122, 255, 0.2);
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}

.add-position-btn {
  width: 60rpx;
  height: 60rpx;
  background: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-position-btn:active {
  background: #0056CC;
}

.add-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

/* 筛选和排序 */
.filter-section {
  border-bottom: 1rpx solid #f2f2f7;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-items {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 16rpx;
}

.filter-picker {
  display: inline-block;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f2f2f7;
  border-radius: 20rpx;
  white-space: nowrap;
}

.filter-item.active {
  background: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.filter-item.reset {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.filter-item:active {
  opacity: 0.7;
}

.filter-text {
  font-size: 26rpx;
  font-weight: 500;
}

.filter-arrow {
  font-size: 20rpx;
  opacity: 0.6;
}

/* 批量操作栏 */
.batch-toolbar {
  background: rgba(0, 122, 255, 0.05);
  border-bottom: 1rpx solid #f2f2f7;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
}

.batch-select {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.select-text {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 500;
}

.select-count {
  font-size: 24rpx;
  color: #666;
}

.batch-buttons {
  display: flex;
  gap: 16rpx;
}

.batch-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
}

.batch-btn.export {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.batch-btn.delete {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.batch-btn:disabled {
  opacity: 0.5;
}

/* 持仓列表项 */
.positions-list {
  padding: 0 30rpx;
}

.position-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f2f2f7;
  transition: all 0.2s ease;
}

.position-item:last-child {
  border-bottom: none;
}

.position-item:active {
  background: rgba(0, 0, 0, 0.05);
}

.position-item.batch-mode {
  padding-left: 0;
}

.position-item.selected {
  background: rgba(0, 122, 255, 0.05);
  border-left: 4rpx solid #007AFF;
}

/* 批量选择框 */
.selection-checkbox {
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background: #007AFF;
  border-color: #007AFF;
}

.check-icon {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.position-info {
  flex: 1;
  margin-right: 20rpx;
}

.position-item.batch-mode .position-info {
  margin-right: 20rpx;
}

.position-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.symbol-name {
  display: flex;
  align-items: center;
}

.symbol {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 12rpx;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.name {
  font-size: 28rpx;
  color: #666;
}

.position-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  font-size: 26rpx;
  color: #007AFF;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: rgba(0, 122, 255, 0.1);
}

.action-btn.delete {
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
}

.action-btn:active {
  opacity: 0.7;
}

.position-details {
  display: flex;
  gap: 30rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
}

.position-value {
  text-align: right;
}

.market-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.unrealized-pnl {
  font-size: 26rpx;
}

.unrealized-pnl.positive {
  color: #34C759;
}

.unrealized-pnl.negative {
  color: #FF3B30;
}

.unrealized-pnl.neutral {
  color: #666;
}

/* 空状态 */
.empty-positions {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 刷新提示 */
.refresh-hint {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(0, 122, 255, 0.9);
  color: white;
  text-align: center;
  padding: 20rpx;
  z-index: 1000;
}

.refresh-text {
  font-size: 28rpx;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #000000;
  }
  
  .account-info-card,
  .positions-section {
    background: #1c1c1e;
  }
  
  .broker-name,
  .title-text,
  .stat-value,
  .symbol,
  .detail-value,
  .market-value,
  .empty-text {
    color: #ffffff;
  }
  
  .account-number,
  .stat-label,
  .position-count,
  .name,
  .detail-label,
  .empty-desc {
    color: #8e8e93;
  }
  
  .section-header {
    border-bottom-color: #3a3a3c;
  }
  
  .position-item {
    border-bottom-color: #3a3a3c;
  }
  
  .position-item:active {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .loading-text {
    color: #8e8e93;
  }
  
  .loading-spinner {
    border-color: #3a3a3c;
    border-top-color: #007AFF;
  }
  
  .filter-section {
    border-bottom-color: #3a3a3c;
  }
  
  .filter-item {
    background: #3a3a3c;
    color: #ffffff;
  }
  
  .filter-item.active {
    background: rgba(0, 122, 255, 0.2);
  }
  
  .filter-item.reset {
    background: rgba(255, 59, 48, 0.2);
  }
  
  .batch-toolbar {
    background: rgba(0, 122, 255, 0.1);
    border-bottom-color: #3a3a3c;
  }
  
  .select-count {
    color: #8e8e93;
  }
  
  .checkbox {
    border-color: #8e8e93;
  }
  
  .position-item.selected {
    background: rgba(0, 122, 255, 0.1);
  }
}