// utils/echarts-config.js
// ECharts 配置文件，用于按需引入组件

// 引入 ECharts 主模块
import * as echarts from 'echarts/lib/echarts'

// 引入需要的图表类型
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/pie'

// 引入需要的组件
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/grid'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/dataZoom'
import 'echarts/lib/component/markPoint'
import 'echarts/lib/component/markLine'

// 引入需要的渲染器
import 'echarts/lib/component/graphic'

export default echarts