// pages/accounts/accounts.js
import dataService from '../../services/DataService.js'

Page({
  data: {
    accounts: [],
    loading: true,
    refreshing: false
  },

  onLoad() {
    console.log('Accounts page loaded')
    this.loadAccounts()
  },

  onShow() {
    this.loadAccounts()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshAccounts()
  },

  async loadAccounts() {
    this.setData({
      loading: true
    })

    try {
      const result = await dataService.getAccounts()
      if (result.success) {
        this.setData({
          accounts: result.data || [],
          loading: false
        })
      } else {
        console.error('加载账户失败:', result.error)
        wx.showToast({
          title: '加载失败',
          icon: 'error'
        })
        this.setData({
          accounts: [],
          loading: false
        })
      }
    } catch (error) {
      console.error('加载账户异常:', error)
      wx.showToast({
        title: '加载异常',
        icon: 'error'
      })
      this.setData({
        accounts: [],
        loading: false
      })
    }
  },

  // 刷新账户数据
  async refreshAccounts() {
    this.setData({
      refreshing: true
    })

    try {
      const result = await dataService.getAccounts()
      if (result.success) {
        this.setData({
          accounts: result.data || [],
          refreshing: false
        })
        wx.showToast({
          title: '刷新成功',
          icon: 'success'
        })
      } else {
        console.error('刷新账户失败:', result.error)
        wx.showToast({
          title: '刷新失败',
          icon: 'error'
        })
        this.setData({
          refreshing: false
        })
      }
    } catch (error) {
      console.error('刷新账户异常:', error)
      wx.showToast({
        title: '刷新异常',
        icon: 'error'
      })
      this.setData({
        refreshing: false
      })
    }

    // 停止下拉刷新
    wx.stopPullDownRefresh()
  },

  // 添加账户
  addAccount() {
    wx.navigateTo({
      url: '/pages/add-account/add-account'
    })
  },

  // 账户卡片点击事件
  onAccountCardTap(e) {
    const { accountId } = e.detail
    wx.navigateTo({
      url: `/pages/account-detail/account-detail?id=${accountId}`
    })
  },

  // 编辑账户
  onEditAccount(e) {
    const { account } = e.detail
    wx.navigateTo({
      url: `/pages/add-account/add-account?mode=edit&id=${account._id}`
    })
  },

  // 删除账户
  async onDeleteAccount(e) {
    const { accountId } = e.detail
    
    wx.showLoading({
      title: '删除中...'
    })

    try {
      const result = await dataService.deleteAccount(accountId)
      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        // 重新加载账户列表
        this.loadAccounts()
      } else {
        wx.showToast({
          title: result.error?.message || '删除失败',
          icon: 'error'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('删除账户异常:', error)
      wx.showToast({
        title: '删除异常',
        icon: 'error'
      })
    }
  },

  // 格式化数值显示
  formatValue(value) {
    if (!value) return '0.00'
    return parseFloat(value).toFixed(2)
  }
})