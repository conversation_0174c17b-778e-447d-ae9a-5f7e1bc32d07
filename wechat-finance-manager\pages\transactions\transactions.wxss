/* pages/transactions/transactions.wxss */
@import "../../styles/common.wxss";

.add-button {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 2rpx dashed #007AFF;
}

.add-icon {
  font-size: 40rpx;
  color: #007AFF;
  margin-right: 16rpx;
}

.add-text {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: 500;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}