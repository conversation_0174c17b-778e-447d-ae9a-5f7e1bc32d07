/**
 * 账户模型单元测试
 */

const Account = require('../../models/Account');

describe('Account Model', () => {
  describe('构造函数', () => {
    test('应该创建默认账户实例', () => {
      const account = new Account();
      
      expect(account._id).toBeNull();
      expect(account.userId).toBe('');
      expect(account.brokerName).toBe('');
      expect(account.accountNumber).toBe('');
      expect(account.accountType).toBe('securities');
      expect(account.totalValue).toBe(0);
      expect(account.dailyChange).toBe(0);
      expect(account.dailyChangePercent).toBe(0);
      expect(account.positions).toEqual([]);
      expect(account.isActive).toBe(true);
      expect(account.displayName).toBe('');
      expect(account.description).toBe('');
      expect(account.createdAt).toBeInstanceOf(Date);
      expect(account.updatedAt).toBeInstanceOf(Date);
    });

    test('应该使用提供的数据创建账户实例', () => {
      const accountData = {
        _id: 'test-account-id',
        userId: 'test-user-id',
        brokerName: '华泰证券',
        accountNumber: '****************',
        accountType: 'securities',
        totalValue: 100000,
        dailyChange: 1500,
        dailyChangePercent: 1.5,
        positions: [{ symbol: '000001', quantity: 1000 }],
        isActive: true,
        displayName: '我的华泰账户',
        description: '主要投资账户'
      };

      const account = new Account(accountData);
      
      expect(account._id).toBe('test-account-id');
      expect(account.userId).toBe('test-user-id');
      expect(account.brokerName).toBe('华泰证券');
      expect(account.accountNumber).toBe('****************');
      expect(account.accountType).toBe('securities');
      expect(account.totalValue).toBe(100000);
      expect(account.dailyChange).toBe(1500);
      expect(account.dailyChangePercent).toBe(1.5);
      expect(account.positions).toHaveLength(1);
      expect(account.isActive).toBe(true);
      expect(account.displayName).toBe('我的华泰账户');
      expect(account.description).toBe('主要投资账户');
    });
  });

  describe('数据验证', () => {
    test('应该验证有效的账户数据', () => {
      const account = new Account({
        userId: 'valid-user-id',
        brokerName: '华泰证券',
        accountNumber: '****************',
        accountType: 'securities'
      });

      const validation = account.validate();
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('应该检测缺少必填字段', () => {
      const account = new Account({
        brokerName: '华泰证券'
      });

      const validation = account.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('用户ID是必填字段且必须为字符串');
      expect(validation.errors).toContain('账户号码是必填字段且必须为字符串');
    });

    test('应该检测无效的账户号码格式', () => {
      const account = new Account({
        userId: 'test-user-id',
        brokerName: '华泰证券',
        accountNumber: '123abc', // 无效格式
        accountType: 'securities'
      });

      const validation = account.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('账户号码必须为10-20位数字');
    });

    test('应该检测无效的账户类型', () => {
      const account = new Account({
        userId: 'test-user-id',
        brokerName: '华泰证券',
        accountNumber: '****************',
        accountType: 'invalid-type'
      });

      const validation = account.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('账户类型只能是securities或fund');
    });

    test('应该检测负数总价值', () => {
      const account = new Account({
        userId: 'test-user-id',
        brokerName: '华泰证券',
        accountNumber: '****************',
        accountType: 'securities',
        totalValue: -1000
      });

      const validation = account.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('总价值必须为非负数');
    });

    test('应该检测过长的显示名称', () => {
      const account = new Account({
        userId: 'test-user-id',
        brokerName: '华泰证券',
        accountNumber: '****************',
        accountType: 'securities',
        displayName: 'a'.repeat(101) // 101个字符
      });

      const validation = account.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('显示名称长度不能超过100个字符');
    });
  });

  describe('数据库操作', () => {
    test('应该转换为数据库存储格式', () => {
      const account = new Account({
        userId: 'test-user-id',
        brokerName: '华泰证券',
        accountNumber: '****************',
        accountType: 'securities',
        totalValue: 100000
      });

      const dbData = account.toDatabase();
      
      expect(dbData.userId).toBe('test-user-id');
      expect(dbData.brokerName).toBe('华泰证券');
      expect(dbData.accountNumber).toBe('****************');
      expect(dbData.accountType).toBe('securities');
      expect(dbData.totalValue).toBe(100000);
      expect(dbData.updatedAt).toBeInstanceOf(Date);
      expect(dbData.createdAt).toBeInstanceOf(Date);
    });

    test('应该从数据库数据创建账户实例', () => {
      const dbData = {
        _id: 'db-account-id',
        userId: 'db-user-id',
        brokerName: '中信证券',
        accountNumber: '****************',
        accountType: 'fund',
        totalValue: 50000,
        dailyChange: -500,
        dailyChangePercent: -1.0,
        positions: [],
        isActive: true,
        displayName: '中信基金账户',
        description: '基金投资专用',
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02')
      };

      const account = Account.fromDatabase(dbData);
      
      expect(account._id).toBe('db-account-id');
      expect(account.userId).toBe('db-user-id');
      expect(account.brokerName).toBe('中信证券');
      expect(account.accountNumber).toBe('****************');
      expect(account.accountType).toBe('fund');
      expect(account.totalValue).toBe(50000);
      expect(account.dailyChange).toBe(-500);
      expect(account.dailyChangePercent).toBe(-1.0);
      expect(account.displayName).toBe('中信基金账户');
      expect(account.description).toBe('基金投资专用');
    });
  });

  describe('账户信息更新', () => {
    test('应该更新账户信息', () => {
      const account = new Account({
        userId: 'test-user-id',
        brokerName: '华泰证券',
        accountNumber: '****************',
        accountType: 'securities'
      });

      const originalUpdatedAt = account.updatedAt;

      setTimeout(() => {
        account.update({
          brokerName: '中信证券',
          totalValue: 200000,
          displayName: '更新后的账户'
        });

        expect(account.brokerName).toBe('中信证券');
        expect(account.totalValue).toBe(200000);
        expect(account.displayName).toBe('更新后的账户');
        expect(account.accountNumber).toBe('****************'); // 保持不变
        expect(account.updatedAt).not.toBe(originalUpdatedAt);
      }, 1);
    });
  });

  describe('显示信息', () => {
    test('应该返回账户显示信息', () => {
      const account = new Account({
        _id: 'display-account-id',
        userId: 'display-user-id',
        brokerName: '华泰证券',
        accountNumber: '****************',
        accountType: 'securities',
        totalValue: 100000,
        dailyChange: 1500,
        positions: [{ symbol: '000001' }, { symbol: '000002' }],
        displayName: '我的华泰账户'
      });

      const displayInfo = account.getDisplayInfo();
      
      expect(displayInfo._id).toBe('display-account-id');
      expect(displayInfo.brokerName).toBe('华泰证券');
      expect(displayInfo.accountNumber).toBe('1234********3456'); // 掩码处理
      expect(displayInfo.accountType).toBe('securities');
      expect(displayInfo.accountTypeName).toBe('证券账户');
      expect(displayInfo.totalValue).toBe(100000);
      expect(displayInfo.dailyChange).toBe(1500);
      expect(displayInfo.positionCount).toBe(2);
      expect(displayInfo.displayName).toBe('我的华泰账户');
      expect(displayInfo.userId).toBeUndefined(); // 不应包含敏感信息
    });

    test('应该为空显示名称提供默认值', () => {
      const account = new Account({
        brokerName: '华泰证券',
        accountNumber: '****************'
      });

      const displayInfo = account.getDisplayInfo();
      
      expect(displayInfo.displayName).toBe('华泰证券-1234********3456');
    });
  });

  describe('账户号码掩码', () => {
    test('应该正确掩码账户号码', () => {
      const account = new Account({
        accountNumber: '****************'
      });

      const maskedNumber = account.maskAccountNumber();
      
      expect(maskedNumber).toBe('1234********3456');
    });

    test('应该处理短账户号码', () => {
      const account = new Account({
        accountNumber: '1234567'
      });

      const maskedNumber = account.maskAccountNumber();
      
      expect(maskedNumber).toBe('1234567'); // 不掩码
    });
  });

  describe('统计信息', () => {
    test('应该计算账户统计信息', () => {
      const account = new Account({
        totalValue: 100000,
        dailyChange: 1500,
        dailyChangePercent: 1.5,
        positions: [
          { quantity: 1000, costPrice: 10 },
          { quantity: 500, costPrice: 20 }
        ]
      });

      const stats = account.getStatistics();
      
      expect(stats.positionCount).toBe(2);
      expect(stats.totalCost).toBe(20000); // 1000*10 + 500*20
      expect(stats.totalMarketValue).toBe(100000);
      expect(stats.totalUnrealizedPnL).toBe(80000); // 100000 - 20000
      expect(stats.totalUnrealizedPnLPercent).toBe(400); // 80000/20000 * 100
      expect(stats.dailyChange).toBe(1500);
      expect(stats.dailyChangePercent).toBe(1.5);
    });
  });

  describe('持仓管理', () => {
    test('应该添加持仓', () => {
      const account = new Account();
      const position = { _id: 'pos1', symbol: '000001', quantity: 1000 };

      account.addPosition(position);
      
      expect(account.positions).toHaveLength(1);
      expect(account.positions[0]).toEqual(position);
    });

    test('应该移除持仓', () => {
      const account = new Account({
        positions: [
          { _id: 'pos1', symbol: '000001' },
          { _id: 'pos2', symbol: '000002' }
        ]
      });

      account.removePosition('pos1');
      
      expect(account.positions).toHaveLength(1);
      expect(account.positions[0]._id).toBe('pos2');
    });

    test('应该更新持仓', () => {
      const account = new Account({
        positions: [
          { _id: 'pos1', symbol: '000001', quantity: 1000 }
        ]
      });

      account.updatePosition('pos1', { quantity: 1500 });
      
      expect(account.positions[0].quantity).toBe(1500);
      expect(account.positions[0].symbol).toBe('000001'); // 保持不变
    });
  });

  describe('权限检查', () => {
    test('应该验证用户权限', () => {
      const account = new Account({
        userId: 'owner-user-id'
      });

      expect(account.checkPermission('owner-user-id')).toBe(true);
      expect(account.checkPermission('other-user-id')).toBe(false);
    });
  });
});