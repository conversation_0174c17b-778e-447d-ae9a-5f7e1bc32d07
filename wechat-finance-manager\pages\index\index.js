// pages/index/index.js
const DataService = require('../../services/DataService')
const pageTransition = require('../../utils/pageTransition')
const ThemeService = require('../../services/ThemeService')

Page({
  data: {
    userId: '',
    theme: 'light',
    accounts: [],
    pageTransition: '',
    assetData: null,
    loading: false,
    error: null
  },

  onLoad() {
    console.log('Index page loaded')
    this.initUser()
    this.initTheme()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadAccountData()
    
    // 更新 tabbar 选中状态
    this.updateTabBar()
  },

  // 初始化用户信息
  async initUser() {
    try {
      // 获取用户信息
      const userInfo = await DataService.getCurrentUser()
      if (userInfo && userInfo._id) {
        this.setData({
          userId: userInfo._id
        })
        console.log('用户ID已设置:', userInfo._id)
      } else {
        console.warn('未获取到用户信息')
        // 可以在这里处理用户未登录的情况
        this.handleUserNotLoggedIn()
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      this.handleUserNotLoggedIn()
    }
  },

  // 处理用户未登录情况
  handleUserNotLoggedIn() {
    wx.showModal({
      title: '提示',
      content: '请先登录后使用',
      showCancel: false,
      success: (res) => {
        if (res.confirm) {
          // 跳转到登录页面或触发登录
          this.triggerLogin()
        }
      }
    })
  },

  // 触发登录
  async triggerLogin() {
    try {
      const loginResult = await DataService.login()
      if (loginResult && loginResult._id) {
        this.setData({
          userId: loginResult._id
        })
        console.log('登录成功，用户ID:', loginResult._id)
      }
    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
    }
  },

  // 初始化主题
  initTheme() {
    try {
      const theme = ThemeService.getCurrentTheme()
      this.setData({ theme })
      
      // 添加主题变化监听器
      this.themeChangeHandler = (newTheme) => {
        this.setData({ theme: newTheme })
      }
      ThemeService.addThemeListener(this.themeChangeHandler)
    } catch (e) {
      console.error('Failed to load theme:', e)
    }
  },

  // 加载账户数据（用于显示账户列表）
  async loadAccountData() {
    try {
      // 获取用户账户列表
      const accounts = await DataService.getAccounts()
      
      this.setData({
        accounts: accounts.slice(0, 3) // 只显示前3个账户
      })
      
      console.log('账户数据加载成功:', accounts.length, '个账户')
    } catch (error) {
      console.error('Failed to load account data:', error)
      this.setData({
        accounts: []
      })
    }
  },

  // 资产概览组件数据更新事件
  onAssetDataUpdated(e) {
    const { assetData } = e.detail
    this.setData({
      assetData: assetData
    })
    console.log('资产数据已更新:', assetData)
  },

  // 资产概览组件错误事件
  onAssetError(e) {
    const { error } = e.detail
    console.error('资产数据加载错误:', error)
    this.setData({
      error: error
    })
  },

  // 资产概览组件刷新事件
  onAssetRefresh() {
    console.log('资产概览刷新事件')
    // 资产概览组件会自动处理刷新，这里可以做额外的处理
  },

  // 点击资产卡片
  onAssetCardTap(e) {
    const { totalAssets, dailyChange, dailyChangePercent } = e.detail
    console.log('资产卡片被点击:', { totalAssets, dailyChange, dailyChangePercent })
    
    // 可以跳转到资产详情页面
    this.navigateToAssetDetail()
  },

  // 查看资产详情
  onViewAssetDetail() {
    this.navigateToAssetDetail()
  },

  // 跳转到资产详情页面
  navigateToAssetDetail() {
    wx.navigateTo({
      url: '/pages/asset-detail/asset-detail'
    })
  },

  // 跳转到图表页面
  navigateToCharts() {
    pageTransition.navigateWithTransition('/pages/asset-chart/asset-chart', {
      transition: 'slide',
      direction: 'left'
    })
  },

  // 跳转到添加交易页面
  navigateToAddTransaction() {
    pageTransition.navigateWithTransition('/pages/add-transaction/add-transaction', {
      transition: 'slide',
      direction: 'left'
    })
  },

  // 跳转到添加持仓页面
  navigateToAddPosition() {
    pageTransition.navigateWithTransition('/pages/add-position/add-position', {
      transition: 'slide',
      direction: 'left'
    })
  },

  // 跳转到添加账户页面
  navigateToAddAccount() {
    pageTransition.navigateWithTransition('/pages/add-account/add-account', {
      transition: 'slide',
      direction: 'left'
    })
  },

  // 跳转到账户列表页面
  navigateToAccounts() {
    wx.switchTab({
      url: '/pages/accounts/accounts'
    })
  },

  // 跳转到账户详情页面
  navigateToAccountDetail(e) {
    const accountId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/account-detail/account-detail?id=${accountId}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 触发资产概览组件刷新
    const assetOverview = this.selectComponent('#asset-overview')
    if (assetOverview) {
      assetOverview.onRefresh().finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
    
    // 同时刷新账户数据
    this.loadAccountData()
  },

  // 更新 tabbar 状态
  updateTabBar(selectedIndex = 0) {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: selectedIndex
      })
    }
  },

  // 页面卸载时清理监听器
  onUnload() {
    if (this.themeChangeHandler) {
      ThemeService.removeThemeListener(this.themeChangeHandler)
    }
  }
})