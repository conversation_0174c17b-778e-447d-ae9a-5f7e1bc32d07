// pages/add-account/add-account.js
import dataService from '../../services/DataService.js'

Page({
  data: {
    mode: 'add', // add 或 edit
    accountId: '',
    brokerName: '',
    accountNumber: '',
    accountType: 'securities', // securities 或 fund
    loading: false,
    brokerList: [
      '华泰证券',
      '中信证券',
      '招商证券',
      '广发证券',
      '国泰君安',
      '海通证券',
      '申万宏源',
      '银河证券',
      '东方财富',
      '平安证券',
      '兴业证券',
      '中金公司',
      '其他'
    ],
    showBrokerPicker: false,
    brokerIndex: -1,
    errors: {}
  },

  onLoad(options) {
    console.log('Add account page loaded', options)
    
    // 检查是否为编辑模式
    if (options.mode === 'edit' && options.id) {
      this.setData({
        mode: 'edit',
        accountId: options.id
      })
      this.loadAccountData(options.id)
    }
  },

  // 加载账户数据（编辑模式）
  async loadAccountData(accountId) {
    wx.showLoading({
      title: '加载中...'
    })

    try {
      // 这里需要实现获取单个账户的方法
      // 暂时使用获取所有账户然后筛选的方式
      const result = await dataService.getAccounts()
      if (result.success) {
        const account = result.data.find(acc => acc._id === accountId)
        if (account) {
          const brokerIndex = this.data.brokerList.indexOf(account.brokerName)
          this.setData({
            brokerName: account.brokerName,
            accountNumber: account.accountNumber,
            accountType: account.accountType || 'securities',
            brokerIndex: brokerIndex >= 0 ? brokerIndex : this.data.brokerList.length - 1
          })
        }
      }
    } catch (error) {
      console.error('加载账户数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }

    wx.hideLoading()
  },

  // 显示券商选择器
  showBrokerPicker() {
    this.setData({
      showBrokerPicker: true
    })
  },

  // 券商选择
  onBrokerChange(e) {
    const index = e.detail.value
    this.setData({
      brokerIndex: index,
      brokerName: this.data.brokerList[index],
      showBrokerPicker: false
    })
    this.clearFieldError('brokerName')
  },

  // 取消券商选择
  onBrokerCancel() {
    this.setData({
      showBrokerPicker: false
    })
  },

  // 账户类型选择
  onAccountTypeChange(e) {
    this.setData({
      accountType: e.detail.value
    })
  },

  // 输入处理
  onBrokerNameInput(e) {
    this.setData({
      brokerName: e.detail.value
    })
    this.clearFieldError('brokerName')
  },

  onAccountNumberInput(e) {
    let value = e.detail.value.replace(/\D/g, '') // 只保留数字
    this.setData({
      accountNumber: value
    })
    this.clearFieldError('accountNumber')
  },

  // 清除字段错误
  clearFieldError(field) {
    const errors = { ...this.data.errors }
    delete errors[field]
    this.setData({ errors })
  },

  // 表单验证
  validateForm() {
    const errors = {}
    const { brokerName, accountNumber } = this.data

    // 验证券商名称
    if (!brokerName.trim()) {
      errors.brokerName = '请选择或输入券商名称'
    }

    // 验证账户号码
    if (!accountNumber.trim()) {
      errors.accountNumber = '请输入账户号码'
    } else if (!/^\d{8,20}$/.test(accountNumber)) {
      errors.accountNumber = '账户号码应为8-20位数字'
    }

    this.setData({ errors })
    return Object.keys(errors).length === 0
  },

  // 提交表单
  async submitForm() {
    if (!this.validateForm()) {
      wx.showToast({
        title: '请检查输入信息',
        icon: 'error'
      })
      return
    }

    this.setData({ loading: true })

    try {
      const accountData = {
        brokerName: this.data.brokerName.trim(),
        accountNumber: this.data.accountNumber.trim(),
        accountType: this.data.accountType,
        totalValue: 0,
        dailyChange: 0,
        dailyChangePercent: 0,
        isActive: true
      }

      let result
      if (this.data.mode === 'edit') {
        result = await dataService.updateAccount(this.data.accountId, accountData)
      } else {
        result = await dataService.createAccount(accountData)
      }

      if (result.success) {
        wx.showToast({
          title: this.data.mode === 'edit' ? '更新成功' : '添加成功',
          icon: 'success'
        })

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.error?.message || '操作失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('提交账户信息失败:', error)
      wx.showToast({
        title: '操作异常',
        icon: 'error'
      })
    }

    this.setData({ loading: false })
  },

  // 删除账户（编辑模式）
  async deleteAccount() {
    if (this.data.mode !== 'edit') return

    wx.showModal({
      title: '确认删除',
      content: '确定要删除此账户吗？删除后无法恢复。',
      confirmText: '删除',
      confirmColor: '#FF3B30',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...'
          })

          try {
            const result = await dataService.deleteAccount(this.data.accountId)
            wx.hideLoading()

            if (result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            } else {
              wx.showToast({
                title: result.error?.message || '删除失败',
                icon: 'error'
              })
            }
          } catch (error) {
            wx.hideLoading()
            console.error('删除账户失败:', error)
            wx.showToast({
              title: '删除异常',
              icon: 'error'
            })
          }
        }
      }
    })
  }
})