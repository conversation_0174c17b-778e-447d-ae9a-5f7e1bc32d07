// utils/chartUtils.js
// 图表工具函数

/**
 * 获取响应式图表尺寸
 * @param {Object} systemInfo 系统信息
 * @param {Number} containerWidth 容器宽度
 * @param {Number} aspectRatio 宽高比，默认2:1
 * @returns {Object} 图表尺寸
 */
export function getResponsiveChartSize(systemInfo, containerWidth, aspectRatio = 2) {
  const { windowWidth, pixelRatio } = systemInfo
  
  // 计算实际像素宽度
  const actualWidth = containerWidth * pixelRatio
  const actualHeight = actualWidth / aspectRatio
  
  // 确保最小高度
  const minHeight = 200 * pixelRatio
  const finalHeight = Math.max(actualHeight, minHeight)
  
  return {
    width: actualWidth,
    height: finalHeight,
    pixelRatio
  }
}

/**
 * 格式化数值显示
 * @param {Number} value 数值
 * @param {String} type 类型：currency, percent, number
 * @returns {String} 格式化后的字符串
 */
export function formatValue(value, type = 'currency') {
  if (typeof value !== 'number' || isNaN(value)) {
    return '--'
  }
  
  switch (type) {
    case 'currency':
      if (Math.abs(value) >= 10000) {
        return `¥${(value / 10000).toFixed(2)}万`
      }
      return `¥${value.toLocaleString()}`
    
    case 'percent':
      return `${(value * 100).toFixed(2)}%`
    
    case 'number':
      if (Math.abs(value) >= 10000) {
        return `${(value / 10000).toFixed(2)}万`
      }
      return value.toLocaleString()
    
    default:
      return value.toString()
  }
}

/**
 * 生成时间范围的日期数组
 * @param {String} timeRange 时间范围
 * @returns {Array} 日期数组
 */
export function generateDateRange(timeRange) {
  const now = new Date()
  const dates = []
  
  switch (timeRange) {
    case 'month':
      // 最近30天
      for (let i = 29; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)
        dates.push(formatDate(date))
      }
      break
    
    case 'quarter':
      // 最近3个月，按周统计
      for (let i = 12; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i * 7)
        dates.push(formatDate(date))
      }
      break
    
    case 'halfYear':
      // 最近6个月，按月统计
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now)
        date.setMonth(date.getMonth() - i)
        dates.push(formatDate(date, 'month'))
      }
      break
    
    case 'year':
      // 最近12个月
      for (let i = 11; i >= 0; i--) {
        const date = new Date(now)
        date.setMonth(date.getMonth() - i)
        dates.push(formatDate(date, 'month'))
      }
      break
    
    case 'all':
      // 根据实际数据范围生成
      // 这里返回空数组，由调用方根据实际数据生成
      break
    
    default:
      dates.push(formatDate(now))
  }
  
  return dates
}

/**
 * 格式化日期
 * @param {Date} date 日期对象
 * @param {String} format 格式类型
 * @returns {String} 格式化后的日期字符串
 */
function formatDate(date, format = 'day') {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  
  switch (format) {
    case 'month':
      return `${month}月`
    case 'day':
      return `${month}/${day}`
    case 'full':
      return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
    default:
      return `${month}/${day}`
  }
}

/**
 * 获取主题色彩配置
 * @param {String} theme 主题类型
 * @returns {Object} 色彩配置
 */
export function getThemeColors(theme = 'light') {
  const themes = {
    light: {
      primary: '#007AFF',
      success: '#34C759',
      warning: '#FF9500',
      danger: '#FF3B30',
      background: '#FFFFFF',
      surface: '#F2F2F7',
      text: '#000000',
      textSecondary: '#8E8E93',
      border: '#C6C6C8',
      grid: '#F0F0F0'
    },
    dark: {
      primary: '#0A84FF',
      success: '#30D158',
      warning: '#FF9F0A',
      danger: '#FF453A',
      background: '#000000',
      surface: '#1C1C1E',
      text: '#FFFFFF',
      textSecondary: '#8E8E93',
      border: '#38383A',
      grid: '#333333'
    }
  }
  
  return themes[theme] || themes.light
}

/**
 * 计算图表数据的统计信息
 * @param {Array} data 图表数据
 * @returns {Object} 统计信息
 */
export function calculateChartStats(data) {
  if (!data || data.length === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      total: 0,
      change: 0,
      changePercent: 0
    }
  }
  
  const values = data.map(item => item.value || 0)
  const min = Math.min(...values)
  const max = Math.max(...values)
  const total = values.reduce((sum, val) => sum + val, 0)
  const avg = total / values.length
  
  // 计算变化
  const firstValue = values[0] || 0
  const lastValue = values[values.length - 1] || 0
  const change = lastValue - firstValue
  const changePercent = firstValue !== 0 ? change / firstValue : 0
  
  return {
    min,
    max,
    avg,
    total,
    change,
    changePercent
  }
}