/**
 * 账户集合配置
 * 定义账户集合的数据库结构、索引和权限规则
 */

const COLLECTION_NAME = 'accounts';

/**
 * 账户集合的数据库索引配置
 */
const INDEXES = [
  {
    // 用户ID索引，用于快速查找用户的所有账户
    keys: { userId: 1 },
    options: { 
      name: 'user_id_index'
    }
  },
  {
    // 账户号码唯一索引，防止重复添加同一账户
    keys: { accountNumber: 1, brokerName: 1 },
    options: { 
      unique: true,
      name: 'account_number_broker_unique'
    }
  },
  {
    // 账户类型索引，用于按类型筛选账户
    keys: { accountType: 1 },
    options: { 
      name: 'account_type_index'
    }
  },
  {
    // 激活状态索引，用于筛选活跃账户
    keys: { isActive: 1 },
    options: { 
      name: 'is_active_index'
    }
  },
  {
    // 创建时间索引，用于按时间排序
    keys: { createdAt: -1 },
    options: { 
      name: 'created_at_desc'
    }
  },
  {
    // 更新时间索引，用于查找最近更新的账户
    keys: { updatedAt: -1 },
    options: { 
      name: 'updated_at_desc'
    }
  },
  {
    // 复合索引：用户ID + 账户类型 + 激活状态
    keys: { userId: 1, accountType: 1, isActive: 1 },
    options: { 
      name: 'user_type_active_index'
    }
  }
];

/**
 * 数据库权限规则
 * 确保用户只能访问自己的账户数据
 */
const PERMISSION_RULES = {
  read: "auth.openid == resource.data.userId",
  write: "auth.openid == resource.data.userId",
  create: "auth.openid == resource.data.userId",
  delete: "auth.openid == resource.data.userId"
};

/**
 * 数据验证规则
 */
const VALIDATION_SCHEMA = {
  required: ['userId', 'brokerName', 'accountNumber', 'accountType'],
  properties: {
    userId: {
      type: 'string',
      minLength: 1
    },
    brokerName: {
      type: 'string',
      minLength: 1,
      maxLength: 50
    },
    accountNumber: {
      type: 'string',
      pattern: '^\\d{10,20}$'
    },
    accountType: {
      type: 'string',
      enum: ['securities', 'fund']
    },
    totalValue: {
      type: 'number',
      minimum: 0
    },
    dailyChange: {
      type: 'number'
    },
    dailyChangePercent: {
      type: 'number'
    },
    positions: {
      type: 'array',
      items: {
        type: 'object'
      }
    },
    isActive: {
      type: 'boolean'
    },
    displayName: {
      type: 'string',
      maxLength: 100
    },
    description: {
      type: 'string',
      maxLength: 500
    },
    createdAt: { type: 'date' },
    updatedAt: { type: 'date' }
  }
};

/**
 * 预定义的券商列表
 */
const BROKER_LIST = [
  '中信证券',
  '华泰证券',
  '国泰君安',
  '海通证券',
  '招商证券',
  '广发证券',
  '申万宏源',
  '中金公司',
  '银河证券',
  '东方证券',
  '兴业证券',
  '方正证券',
  '光大证券',
  '平安证券',
  '中投证券',
  '长江证券',
  '国信证券',
  '华西证券',
  '东北证券',
  '西南证券',
  '其他券商'
];

/**
 * 初始化账户集合
 * 创建索引和设置权限规则
 */
async function initializeCollection(db) {
  try {
    const collection = db.collection(COLLECTION_NAME);
    
    // 创建索引
    for (const index of INDEXES) {
      await collection.createIndex(index.keys, index.options);
      console.log(`Created index: ${index.options.name}`);
    }
    
    console.log(`Account collection initialized successfully`);
    return true;
  } catch (error) {
    console.error('Failed to initialize account collection:', error);
    return false;
  }
}

module.exports = {
  COLLECTION_NAME,
  INDEXES,
  PERMISSION_RULES,
  VALIDATION_SCHEMA,
  BROKER_LIST,
  initializeCollection
};