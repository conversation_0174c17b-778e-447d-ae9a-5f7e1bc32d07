/* pages/add-account/add-account.wxss */
@import "../../styles/common.wxss";

.container {
  padding: 40rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid #e5e5e7;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
}

.form-input.error {
  border-color: #FF3B30;
}

.form-input.picker-input {
  color: #333;
}

.picker-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.picker-arrow {
  position: absolute;
  right: 24rpx;
  font-size: 32rpx;
  color: #c7c7cc;
  transform: rotate(90deg);
}

.radio-group {
  display: flex;
  gap: 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.radio-text {
  font-size: 30rpx;
  color: #333;
}

.error-text {
  font-size: 26rpx;
  color: #FF3B30;
  margin-top: 8rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.btn-primary {
  background: #007AFF;
  color: white;
}

.btn-primary:active {
  background: #0056CC;
}

.btn-primary[disabled] {
  background: #c7c7cc;
  color: #8e8e93;
}

.btn-danger {
  background: #FF3B30;
  color: white;
}

.btn-danger:active {
  background: #D70015;
}

.btn-danger[disabled] {
  background: #c7c7cc;
  color: #8e8e93;
}

/* 券商选择器样式 */
.broker-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.broker-picker.show {
  opacity: 1;
  visibility: visible;
}

.picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.picker-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 60vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.broker-picker.show .picker-content {
  transform: translateY(0);
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #e5e5e7;
}

.picker-cancel,
.picker-confirm {
  font-size: 32rpx;
  color: #007AFF;
}

.picker-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.picker-column {
  max-height: 400rpx;
  overflow-y: auto;
}

.picker-item {
  padding: 30rpx 40rpx;
  font-size: 32rpx;
  color: #333;
  border-bottom: 1rpx solid #f2f2f7;
}

.picker-item:active {
  background: rgba(0, 0, 0, 0.05);
}

.picker-item.selected {
  color: #007AFF;
  background: rgba(0, 122, 255, 0.1);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #000000;
  }
  
  .form-label {
    color: #ffffff;
  }
  
  .form-input {
    background: #1c1c1e;
    border-color: #3a3a3c;
    color: #ffffff;
  }
  
  .form-input:focus {
    border-color: #007AFF;
  }
  
  .radio-text {
    color: #ffffff;
  }
  
  .picker-content {
    background: #1c1c1e;
  }
  
  .picker-title {
    color: #ffffff;
  }
  
  .picker-item {
    color: #ffffff;
    border-bottom-color: #3a3a3c;
  }
  
  .picker-item:active {
    background: rgba(255, 255, 255, 0.1);
  }
}