// services/AuthService.js
import CryptoJS from 'crypto-js'

class AuthService {
  constructor() {
    this.db = wx.cloud.database()
    this.encryptionKey = 'auth-service-key-2024'
    this.logger = this.initLogger()
    this.permissions = {
      'account:read': ['user', 'admin'],
      'account:write': ['user', 'admin'],
      'account:delete': ['admin'],
      'transaction:read': ['user', 'admin'],
      'transaction:write': ['user', 'admin'],
      'system:admin': ['admin']
    }
  }

  // 初始化日志记录器
  initLogger() {
    return {
      info: (message, data = {}) => {
        console.log(`[AuthService INFO] ${message}`, data)
        this.saveLog('info', message, data)
      },
      error: (message, error = {}) => {
        console.error(`[AuthService ERROR] ${message}`, error)
        this.saveLog('error', message, error)
      },
      warn: (message, data = {}) => {
        console.warn(`[AuthService WARN] ${message}`, data)
        this.saveLog('warn', message, data)
      }
    }
  }

  // 保存日志到云数据库
  async saveLog(level, message, data) {
    try {
      await this.db.collection('logs').add({
        data: {
          level,
          message,
          data: JSON.stringify(data),
          userId: this.getCurrentUserId(),
          timestamp: new Date(),
          source: 'AuthService'
        }
      })
    } catch (error) {
      console.error('保存日志失败:', error)
    }
  }

  // 错误处理包装器
  async executeWithErrorHandling(operation, operationName) {
    try {
      this.logger.info(`开始执行操作: ${operationName}`)
      const result = await operation()
      this.logger.info(`操作成功完成: ${operationName}`, { result })
      return { success: true, data: result }
    } catch (error) {
      this.logger.error(`操作失败: ${operationName}`, error)
      return { 
        success: false, 
        error: {
          message: error.message || '操作失败',
          code: error.code || 'UNKNOWN_ERROR',
          details: error
        }
      }
    }
  }

  // 微信登录
  async login() {
    return await this.executeWithErrorHandling(async () => {
      // 获取微信登录凭证
      const loginResult = await wx.cloud.callFunction({
        name: 'login'
      })

      if (!loginResult.result.success) {
        throw new Error(loginResult.result.error || '登录失败')
      }

      const userInfo = loginResult.result.data
      
      // 验证用户信息
      if (!userInfo.openid) {
        throw new Error('获取用户标识失败')
      }

      // 检查用户是否已存在，不存在则创建
      await this.createOrUpdateUser(userInfo)
      
      // 加密用户信息后保存到本地存储
      const encryptedUserInfo = this.encryptData(userInfo)
      wx.setStorageSync('userInfo', encryptedUserInfo)
      
      // 更新全局状态
      const app = getApp()
      app.globalData.userInfo = userInfo
      app.globalData.isLoggedIn = true

      this.logger.info('用户登录成功', { openid: userInfo.openid })
      return userInfo
    }, 'login')
  }

  // 创建或更新用户信息
  async createOrUpdateUser(userInfo) {
    try {
      const existingUser = await this.db.collection('users')
        .where({ openid: userInfo.openid })
        .get()

      if (existingUser.data.length === 0) {
        // 创建新用户
        await this.db.collection('users').add({
          data: {
            ...userInfo,
            role: 'user', // 默认角色
            settings: {
              autoRefresh: true,
              notifications: true,
              defaultTimeRange: 'month'
            },
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
        this.logger.info('创建新用户', { openid: userInfo.openid })
      } else {
        // 更新现有用户
        await this.db.collection('users')
          .doc(existingUser.data[0]._id)
          .update({
            data: {
              nickName: userInfo.nickName,
              avatarUrl: userInfo.avatarUrl,
              updatedAt: new Date()
            }
          })
        this.logger.info('更新用户信息', { openid: userInfo.openid })
      }
    } catch (error) {
      this.logger.error('创建或更新用户失败', error)
      throw error
    }
  }

  // 获取用户信息
  async getUserInfo() {
    return await this.executeWithErrorHandling(async () => {
      const encryptedUserInfo = wx.getStorageSync('userInfo')
      if (encryptedUserInfo) {
        // 解密用户信息
        const userInfo = this.decryptData(encryptedUserInfo)
        
        // 验证用户信息是否有效
        if (userInfo && userInfo.openid) {
          return userInfo
        }
      }
      
      // 如果本地没有有效的用户信息，尝试重新登录
      const loginResult = await this.login()
      if (loginResult.success) {
        return loginResult.data
      } else {
        throw new Error('获取用户信息失败，请重新登录')
      }
    }, 'getUserInfo')
  }

  // 获取用户详细信息（从数据库）
  async getUserProfile(openid) {
    return await this.executeWithErrorHandling(async () => {
      const userId = openid || this.getCurrentUserId()
      if (!userId) {
        throw new Error('用户ID不能为空')
      }

      const result = await this.db.collection('users')
        .where({ openid: userId })
        .get()

      if (result.data.length === 0) {
        throw new Error('用户不存在')
      }

      return result.data[0]
    }, 'getUserProfile')
  }

  // 检查权限
  async checkPermission(resource, action = 'read') {
    return await this.executeWithErrorHandling(async () => {
      const userInfo = await this.getUserInfo()
      if (!userInfo.success) {
        throw new Error('用户未登录')
      }

      // 获取用户角色
      const userProfile = await this.getUserProfile(userInfo.data.openid)
      if (!userProfile.success) {
        throw new Error('获取用户角色失败')
      }

      const userRole = userProfile.data.role || 'user'
      const permissionKey = `${resource}:${action}`
      
      // 检查权限
      const allowedRoles = this.permissions[permissionKey] || []
      const hasPermission = allowedRoles.includes(userRole)

      this.logger.info('权限检查', { 
        resource, 
        action, 
        userRole, 
        hasPermission 
      })

      return { 
        hasPermission, 
        userRole, 
        resource, 
        action 
      }
    }, 'checkPermission')
  }

  // 检查数据访问权限
  async checkDataAccess(dataUserId, requiredPermission = null) {
    return await this.executeWithErrorHandling(async () => {
      const currentUserId = this.getCurrentUserId()
      
      // 用户只能访问自己的数据，除非有管理员权限
      if (dataUserId === currentUserId) {
        return { hasAccess: true, reason: 'owner' }
      }

      // 检查是否有管理员权限
      if (requiredPermission) {
        const permissionResult = await this.checkPermission('system', 'admin')
        if (permissionResult.success && permissionResult.data.hasPermission) {
          return { hasAccess: true, reason: 'admin' }
        }
      }

      return { hasAccess: false, reason: 'unauthorized' }
    }, 'checkDataAccess')
  }

  // 注销登录
  async logout() {
    return await this.executeWithErrorHandling(async () => {
      const currentUserId = this.getCurrentUserId()
      
      // 清除本地存储
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('userSettings')
      wx.removeStorageSync('cacheData')
      
      // 更新全局状态
      const app = getApp()
      app.globalData.userInfo = null
      app.globalData.isLoggedIn = false

      this.logger.info('用户注销成功', { openid: currentUserId })
      return { message: '注销成功' }
    }, 'logout')
  }

  // 更新用户设置
  async updateUserSettings(settings) {
    return await this.executeWithErrorHandling(async () => {
      const currentUserId = this.getCurrentUserId()
      if (!currentUserId) {
        throw new Error('用户未登录')
      }

      // 验证设置数据
      this.validateUserSettings(settings)

      const result = await this.db.collection('users')
        .where({ openid: currentUserId })
        .update({
          data: {
            settings: settings,
            updatedAt: new Date()
          }
        })

      // 更新本地缓存
      wx.setStorageSync('userSettings', settings)

      return result
    }, 'updateUserSettings')
  }

  // 验证用户设置
  validateUserSettings(settings) {
    if (typeof settings !== 'object') {
      throw new Error('设置数据格式不正确')
    }

    if (settings.autoRefresh !== undefined && typeof settings.autoRefresh !== 'boolean') {
      throw new Error('自动刷新设置必须为布尔值')
    }

    if (settings.notifications !== undefined && typeof settings.notifications !== 'boolean') {
      throw new Error('通知设置必须为布尔值')
    }

    if (settings.defaultTimeRange && !['week', 'month', 'quarter', 'year'].includes(settings.defaultTimeRange)) {
      throw new Error('默认时间范围设置不正确')
    }
  }

  // 数据加密
  encryptData(data, key) {
    try {
      if (!data) return data
      const encryptionKey = key || this.encryptionKey
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), encryptionKey).toString()
      return encrypted
    } catch (error) {
      this.logger.error('数据加密失败', error)
      return data // 加密失败时返回原数据
    }
  }

  // 数据解密
  decryptData(encryptedData, key) {
    try {
      if (!encryptedData) return encryptedData
      const encryptionKey = key || this.encryptionKey
      const bytes = CryptoJS.AES.decrypt(encryptedData, encryptionKey)
      const decrypted = bytes.toString(CryptoJS.enc.Utf8)
      return JSON.parse(decrypted)
    } catch (error) {
      this.logger.error('数据解密失败', error)
      return encryptedData // 解密失败时返回原数据
    }
  }

  // 检查登录状态
  isLoggedIn() {
    try {
      const app = getApp()
      const isLoggedIn = app.globalData.isLoggedIn
      const userInfo = wx.getStorageSync('userInfo')
      
      // 双重检查：全局状态和本地存储都要有效
      return isLoggedIn && userInfo
    } catch (error) {
      this.logger.error('检查登录状态失败', error)
      return false
    }
  }

  // 获取当前用户ID
  getCurrentUserId() {
    try {
      const app = getApp()
      return app.globalData.userInfo?.openid || ''
    } catch (error) {
      this.logger.error('获取用户ID失败', error)
      return ''
    }
  }

  // 刷新用户token（如果需要）
  async refreshToken() {
    return await this.executeWithErrorHandling(async () => {
      // 微信小程序通常不需要刷新token，但可以重新获取用户信息
      const result = await this.login()
      if (result.success) {
        return { refreshed: true, userInfo: result.data }
      } else {
        throw new Error('刷新用户信息失败')
      }
    }, 'refreshToken')
  }

  // 验证用户会话
  async validateSession() {
    return await this.executeWithErrorHandling(async () => {
      if (!this.isLoggedIn()) {
        throw new Error('用户未登录')
      }

      const userInfo = await this.getUserInfo()
      if (!userInfo.success) {
        throw new Error('用户会话无效')
      }

      // 检查用户是否仍然存在于数据库中
      const userProfile = await this.getUserProfile(userInfo.data.openid)
      if (!userProfile.success) {
        throw new Error('用户账户不存在')
      }

      return { 
        valid: true, 
        userInfo: userInfo.data,
        profile: userProfile.data
      }
    }, 'validateSession')
  }
}

// 导出单例
const authService = new AuthService()
export default authService