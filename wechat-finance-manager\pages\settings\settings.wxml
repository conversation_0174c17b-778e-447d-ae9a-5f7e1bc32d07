<!--pages/settings/settings.wxml-->
<view class="container page-transition {{pageTransition}}" data-theme="{{theme}}">
  <!-- 用户信息 -->
  <view class="user-section" wx:if="{{userInfo}}">
    <view class="user-card">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-info">
        <view class="user-name">{{userInfo.nickName}}</view>
        <view class="user-desc">理财管家用户</view>
      </view>
    </view>
  </view>

  <!-- 主题设置 -->
  <view class="settings-section">
    <view class="section-title">外观设置</view>
    <view class="theme-setting-card">
      <view class="setting-header">
        <view class="setting-title">主题模式</view>
        <view class="setting-desc">选择您喜欢的界面风格</view>
      </view>
      <theme-switcher 
        theme="{{theme}}"
        show-quick-switch="{{false}}"
        bind:themechange="onThemeChange">
      </theme-switcher>
    </view>
  </view>

  <!-- 应用设置 -->
  <view class="settings-section">
    <view class="section-title">应用设置</view>
    <view class="settings-list">
      <view class="setting-item">
        <view class="setting-content">
          <view class="setting-icon">🔄</view>
          <view class="setting-info">
            <view class="setting-label">自动刷新</view>
            <view class="setting-desc">自动获取最新价格数据</view>
          </view>
        </view>
        <switch checked="{{autoRefresh}}" bindchange="onAutoRefreshChange" color="var(--primary-color)"></switch>
      </view>

      <view class="setting-item">
        <view class="setting-content">
          <view class="setting-icon">🔔</view>
          <view class="setting-info">
            <view class="setting-label">消息通知</view>
            <view class="setting-desc">接收价格变动提醒</view>
          </view>
        </view>
        <switch checked="{{notifications}}" bindchange="onNotificationChange" color="var(--primary-color)"></switch>
      </view>

      <view class="setting-item" bindtap="navigateToAbout">
        <view class="setting-content">
          <view class="setting-icon">ℹ️</view>
          <view class="setting-info">
            <view class="setting-label">关于应用</view>
            <view class="setting-desc">版本信息和帮助</view>
          </view>
        </view>
        <view class="setting-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 数据管理 -->
  <view class="settings-section">
    <view class="section-title">数据管理</view>
    <view class="settings-list">
      <view class="setting-item" bindtap="exportData">
        <view class="setting-content">
          <view class="setting-icon">📤</view>
          <view class="setting-info">
            <view class="setting-label">导出数据</view>
            <view class="setting-desc">导出您的投资数据</view>
          </view>
        </view>
        <view class="setting-arrow">></view>
      </view>

      <view class="setting-item" bindtap="clearCache">
        <view class="setting-content">
          <view class="setting-icon">🗑️</view>
          <view class="setting-info">
            <view class="setting-label">清除缓存</view>
            <view class="setting-desc">清理本地缓存数据</view>
          </view>
        </view>
        <view class="setting-arrow">></view>
      </view>
    </view>
  </view>
</view>