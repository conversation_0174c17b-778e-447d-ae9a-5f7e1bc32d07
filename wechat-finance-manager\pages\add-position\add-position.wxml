<!--pages/add-position/add-position.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 表单内容 -->
  <view wx:else class="form-container">
    <!-- 证券代码 -->
    <view class="form-group">
      <view class="form-label">
        <text class="label-text">证券代码</text>
        <text class="required">*</text>
      </view>
      <view class="input-container">
        <input
          class="form-input {{errors.symbol ? 'error' : ''}}"
          type="text"
          placeholder="请输入6位证券代码"
          value="{{formData.symbol}}"
          data-field="symbol"
          bindinput="onInputChange"
          maxlength="6"
        />
        <view wx:if="{{symbolValidating}}" class="input-loading">
          <view class="loading-spinner small"></view>
        </view>
      </view>
      <view wx:if="{{errors.symbol}}" class="error-text">{{errors.symbol}}</view>
      <view wx:if="{{securityInfo.isValid}}" class="success-text">
        {{securityInfo.marketName}}
      </view>
    </view>

    <!-- 证券名称 -->
    <view class="form-group">
      <view class="form-label">
        <text class="label-text">证券名称</text>
        <text class="optional">（可选）</text>
      </view>
      <input
        class="form-input {{errors.name ? 'error' : ''}}"
        type="text"
        placeholder="请输入证券名称"
        value="{{formData.name}}"
        data-field="name"
        bindinput="onInputChange"
        maxlength="100"
      />
      <view wx:if="{{errors.name}}" class="error-text">{{errors.name}}</view>
    </view>

    <!-- 证券类型 -->
    <view class="form-group">
      <view class="form-label">
        <text class="label-text">证券类型</text>
        <text class="required">*</text>
      </view>
      <picker
        class="form-picker"
        mode="selector"
        range="{{securityTypes}}"
        range-key="label"
        value="{{formData.type}}"
        bindchange="onTypeChange"
      >
        <view class="picker-display {{errors.type ? 'error' : ''}}">
          <text class="picker-text">{{getSecurityTypeName(formData.type)}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
      <view wx:if="{{errors.type}}" class="error-text">{{errors.type}}</view>
    </view>

    <!-- 持仓数量 -->
    <view class="form-group">
      <view class="form-label">
        <text class="label-text">持仓数量</text>
        <text class="required">*</text>
      </view>
      <view class="input-container">
        <input
          class="form-input {{errors.quantity ? 'error' : ''}}"
          type="digit"
          placeholder="请输入持仓数量"
          value="{{formData.quantity}}"
          data-field="quantity"
          bindinput="formatNumberInput"
        />
        <text class="input-unit">股/份</text>
      </view>
      <view wx:if="{{errors.quantity}}" class="error-text">{{errors.quantity}}</view>
    </view>

    <!-- 成本价 -->
    <view class="form-group">
      <view class="form-label">
        <text class="label-text">成本价</text>
        <text class="required">*</text>
      </view>
      <view class="input-container">
        <input
          class="form-input {{errors.costPrice ? 'error' : ''}}"
          type="digit"
          placeholder="请输入成本价"
          value="{{formData.costPrice}}"
          data-field="costPrice"
          bindinput="formatNumberInput"
        />
        <text class="input-unit">元</text>
      </view>
      <view wx:if="{{errors.costPrice}}" class="error-text">{{errors.costPrice}}</view>
    </view>

    <!-- 备注 -->
    <view class="form-group">
      <view class="form-label">
        <text class="label-text">备注</text>
        <text class="optional">（可选）</text>
      </view>
      <textarea
        class="form-textarea {{errors.notes ? 'error' : ''}}"
        placeholder="请输入备注信息"
        value="{{formData.notes}}"
        data-field="notes"
        bindinput="onInputChange"
        maxlength="500"
        show-confirm-bar="{{false}}"
        auto-height
      />
      <view class="textarea-counter">
        <text class="counter-text">{{formData.notes.length}}/500</text>
      </view>
      <view wx:if="{{errors.notes}}" class="error-text">{{errors.notes}}</view>
    </view>

    <!-- 计算预览 -->
    <view wx:if="{{formData.quantity && formData.costPrice}}" class="preview-container">
      <view class="preview-title">持仓预览</view>
      <view class="preview-item">
        <text class="preview-label">总成本：</text>
        <text class="preview-value">¥{{(formData.quantity * formData.costPrice).toFixed(2)}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-container">
      <button
        class="btn btn-secondary"
        bindtap="resetForm"
        disabled="{{submitting}}"
      >
        重置
      </button>
      <button
        class="btn btn-primary"
        bindtap="submitForm"
        disabled="{{submitting}}"
        loading="{{submitting}}"
      >
        {{mode === 'edit' ? '更新持仓' : '添加持仓'}}
      </button>
    </view>
  </view>
</view>