/* custom-tab-bar/index.wxss */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--background-primary);
  border-top: 1rpx solid var(--border-color);
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 1000;
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

.tabbar-container {
  display: flex;
  height: 100rpx;
  position: relative;
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.tabbar-item.active {
  transform: translateY(-4rpx);
}

.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tabbar-item.active .tabbar-icon {
  transform: scale(1.15);
}

.icon-text {
  font-size: 40rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tabbar-item.active .icon-text {
  filter: drop-shadow(0 2rpx 8rpx rgba(0, 122, 255, 0.4));
}

.tabbar-text {
  font-size: 20rpx;
  color: var(--text-tertiary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 400;
}

.tabbar-item.active .tabbar-text {
  color: var(--primary-color);
  font-weight: 600;
  transform: scale(1.05);
}

.active-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 20%;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2rpx 2rpx 0 0;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 -2rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* 深色模式适配 */
.custom-tabbar.dark {
  background: rgba(28, 28, 30, 0.9);
  border-top-color: var(--border-color);
}

/* 点击效果 */
.tabbar-item:active {
  background: var(--background-secondary);
  border-radius: 16rpx;
  transform: scale(0.95);
}

.tabbar-item.active:active {
  transform: translateY(-4rpx) scale(0.95);
}

/* 涟漪效果 */
.tabbar-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 122, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.tabbar-item:active::before {
  width: 80rpx;
  height: 80rpx;
}