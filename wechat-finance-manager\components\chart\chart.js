// components/chart/chart.js
import echarts from '../../utils/echarts-config'
import { getThemeColors, formatValue, calculateChartStats } from '../../utils/chartUtils'

Component({
  properties: {
    data: {
      type: Array,
      value: []
    },
    timeRange: {
      type: String,
      value: 'month'
    },
    theme: {
      type: String,
      value: 'light'
    },
    height: {
      type: Number,
      value: 400
    },
    chartType: {
      type: String,
      value: 'line' // line, bar, pie
    }
  },

  data: {
    ec: {
      onInit: null
    },
    echarts: echarts,
    loading: true,
    isEmpty: false
  },

  lifetimes: {
    attached() {
      // 初始化图表配置
      this.setData({
        ec: {
          onInit: this.initChart.bind(this)
        }
      })
    },

    ready() {
      // ECharts组件会自动调用onInit
    },

    detached() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  },

  observers: {
    'data, timeRange, theme, chartType': function(data, timeRange, theme, chartType) {
      if (this.chart) {
        this.updateChart()
      }
    }
  },

  methods: {
    // 初始化图表
    initChart(canvas, width, height, dpr) {
      const chart = echarts.init(canvas, null, {
        width: width,
        height: height,
        devicePixelRatio: dpr
      })
      
      this.chart = chart
      this.setData({ loading: false })
      
      // 初始渲染
      this.renderChart()
      
      return chart
    },

    // 渲染图表
    renderChart() {
      if (!this.chart) return
      
      const { data, theme, chartType } = this.properties
      
      if (!data || data.length === 0) {
        this.setData({ isEmpty: true })
        this.triggerEvent('error', { message: '暂无数据' })
        return
      }

      this.setData({ isEmpty: false })
      
      const option = this.getChartOption(data, theme, chartType)
      this.chart.setOption(option, true)
      
      this.triggerEvent('ready', { chart: this.chart })
    },

    // 获取图表配置
    getChartOption(data, theme, chartType) {
      const colors = getThemeColors(theme)
      const stats = calculateChartStats(data)
      
      // 处理数据格式
      const xAxisData = data.map(item => item.date || item.label)
      const seriesData = data.map(item => item.value)
      
      const baseOption = {
        backgroundColor: colors.background,
        textStyle: {
          color: colors.text,
          fontSize: 12
        },
        grid: {
          left: '8%',
          right: '8%',
          top: '15%',
          bottom: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: colors.textSecondary,
            fontSize: 10,
            margin: 8
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: colors.textSecondary,
            fontSize: 10,
            formatter: (value) => formatValue(value, 'number').replace('¥', '')
          },
          splitLine: {
            lineStyle: {
              color: colors.grid,
              type: 'solid',
              width: 0.5
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: colors.surface,
          borderColor: colors.border,
          borderWidth: 1,
          textStyle: {
            color: colors.text,
            fontSize: 12
          },
          formatter: (params) => {
            const param = params[0]
            const value = formatValue(param.value, 'currency')
            return `${param.name}<br/>资产价值: ${value}`
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: colors.primary,
              width: 1,
              type: 'solid'
            }
          }
        },
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }

      // 根据图表类型设置series
      if (chartType === 'line') {
        // 判断数据趋势，设置颜色
        const isPositive = stats.change >= 0
        const lineColor = isPositive ? colors.success : colors.danger
        
        baseOption.series = [{
          type: 'line',
          data: seriesData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            color: lineColor,
            width: 2
          },
          itemStyle: {
            color: lineColor,
            borderWidth: 2,
            borderColor: colors.background
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: lineColor.replace('rgb', 'rgba').replace(')', ', 0.3)')
              }, {
                offset: 1,
                color: lineColor.replace('rgb', 'rgba').replace(')', ', 0.05)')
              }]
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: lineColor
            }
          }
        }]
      } else if (chartType === 'bar') {
        baseOption.series = [{
          type: 'bar',
          data: seriesData,
          itemStyle: {
            color: colors.primary,
            borderRadius: [2, 2, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: colors.primary
            }
          }
        }]
      }

      return baseOption
    },

    // 更新图表数据
    updateChart() {
      if (!this.chart) return
      
      this.renderChart()
      this.triggerEvent('update', { chart: this.chart })
    },

    // 获取图表实例
    getChart() {
      return this.chart
    },

    // 导出图表图片
    exportImage() {
      return new Promise((resolve, reject) => {
        if (!this.chart) {
          reject(new Error('图表未初始化'))
          return
        }

        // 使用ECharts的导出功能
        this.selectComponent('#ec-canvas').canvasToTempFilePath({
          success: (res) => {
            resolve(res.tempFilePath)
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    },

    // 处理图表点击事件
    onChartClick(e) {
      this.triggerEvent('chartClick', e.detail)
    }
  }
})