/**
 * 用户模型单元测试
 */

const User = require('../../models/User');

describe('User Model', () => {
  describe('构造函数', () => {
    test('应该创建默认用户实例', () => {
      const user = new User();
      
      expect(user._id).toBeNull();
      expect(user.openid).toBe('');
      expect(user.nickName).toBe('');
      expect(user.avatarUrl).toBe('');
      expect(user.theme).toBe('light');
      expect(user.settings.autoRefresh).toBe(true);
      expect(user.settings.notifications).toBe(true);
      expect(user.settings.defaultTimeRange).toBe('month');
      expect(user.createdAt).toBeInstanceOf(Date);
      expect(user.updatedAt).toBeInstanceOf(Date);
    });

    test('应该使用提供的数据创建用户实例', () => {
      const userData = {
        _id: 'test-id',
        openid: 'test-openid',
        nickName: '测试用户',
        avatarUrl: 'https://example.com/avatar.jpg',
        theme: 'dark',
        settings: {
          autoRefresh: false,
          notifications: false,
          defaultTimeRange: 'year'
        }
      };

      const user = new User(userData);
      
      expect(user._id).toBe('test-id');
      expect(user.openid).toBe('test-openid');
      expect(user.nickName).toBe('测试用户');
      expect(user.avatarUrl).toBe('https://example.com/avatar.jpg');
      expect(user.theme).toBe('dark');
      expect(user.settings.autoRefresh).toBe(false);
      expect(user.settings.notifications).toBe(false);
      expect(user.settings.defaultTimeRange).toBe('year');
    });
  });

  describe('数据验证', () => {
    test('应该验证有效的用户数据', () => {
      const user = new User({
        openid: 'valid-openid',
        nickName: '有效用户',
        theme: 'light'
      });

      const validation = user.validate();
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('应该检测缺少openid', () => {
      const user = new User({
        nickName: '测试用户'
      });

      const validation = user.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('openid是必填字段且必须为字符串');
    });

    test('应该检测无效的主题', () => {
      const user = new User({
        openid: 'test-openid',
        theme: 'invalid-theme'
      });

      const validation = user.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('主题只能是light或dark');
    });

    test('应该检测昵称长度超限', () => {
      const user = new User({
        openid: 'test-openid',
        nickName: 'a'.repeat(51) // 51个字符
      });

      const validation = user.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('昵称长度不能超过50个字符');
    });

    test('应该检测无效的时间范围设置', () => {
      const user = new User({
        openid: 'test-openid',
        settings: {
          defaultTimeRange: 'invalid-range'
        }
      });

      const validation = user.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('默认时间范围设置无效');
    });
  });

  describe('数据库操作', () => {
    test('应该转换为数据库存储格式', () => {
      const user = new User({
        openid: 'test-openid',
        nickName: '测试用户',
        theme: 'dark'
      });

      const dbData = user.toDatabase();
      
      expect(dbData.openid).toBe('test-openid');
      expect(dbData.nickName).toBe('测试用户');
      expect(dbData.theme).toBe('dark');
      expect(dbData.updatedAt).toBeInstanceOf(Date);
      expect(dbData.createdAt).toBeInstanceOf(Date);
    });

    test('应该从数据库数据创建用户实例', () => {
      const dbData = {
        _id: 'db-id',
        openid: 'db-openid',
        nickName: '数据库用户',
        avatarUrl: 'https://example.com/db-avatar.jpg',
        theme: 'light',
        settings: {
          autoRefresh: true,
          notifications: false,
          defaultTimeRange: 'quarter'
        },
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02')
      };

      const user = User.fromDatabase(dbData);
      
      expect(user._id).toBe('db-id');
      expect(user.openid).toBe('db-openid');
      expect(user.nickName).toBe('数据库用户');
      expect(user.avatarUrl).toBe('https://example.com/db-avatar.jpg');
      expect(user.theme).toBe('light');
      expect(user.settings.autoRefresh).toBe(true);
      expect(user.settings.notifications).toBe(false);
      expect(user.settings.defaultTimeRange).toBe('quarter');
    });
  });

  describe('用户信息更新', () => {
    test('应该更新用户信息', () => {
      const user = new User({
        openid: 'test-openid',
        nickName: '原始用户',
        theme: 'light'
      });

      const originalUpdatedAt = user.updatedAt;

      // 等待一毫秒确保时间戳不同
      setTimeout(() => {
        user.update({
          nickName: '更新用户',
          theme: 'dark',
          settings: {
            autoRefresh: false
          }
        });

        expect(user.nickName).toBe('更新用户');
        expect(user.theme).toBe('dark');
        expect(user.settings.autoRefresh).toBe(false);
        expect(user.settings.notifications).toBe(true); // 保持原有设置
        expect(user.updatedAt).not.toBe(originalUpdatedAt);
      }, 1);
    });
  });

  describe('显示信息', () => {
    test('应该返回用户显示信息', () => {
      const user = new User({
        _id: 'display-id',
        openid: 'display-openid',
        nickName: '显示用户',
        avatarUrl: 'https://example.com/display-avatar.jpg',
        theme: 'dark'
      });

      const displayInfo = user.getDisplayInfo();
      
      expect(displayInfo._id).toBe('display-id');
      expect(displayInfo.nickName).toBe('显示用户');
      expect(displayInfo.avatarUrl).toBe('https://example.com/display-avatar.jpg');
      expect(displayInfo.theme).toBe('dark');
      expect(displayInfo.settings).toBeDefined();
      expect(displayInfo.openid).toBeUndefined(); // 不应包含敏感信息
    });

    test('应该为空昵称提供默认值', () => {
      const user = new User({
        openid: 'test-openid'
      });

      const displayInfo = user.getDisplayInfo();
      
      expect(displayInfo.nickName).toBe('未设置昵称');
    });
  });
});