// components/asset-overview/asset-overview.js
import assetCalculationService from '../../services/AssetCalculationService.js'

Component({
  properties: {
    userId: {
      type: String,
      value: ''
    },
    theme: {
      type: String,
      value: 'light'
    },
    autoRefresh: {
      type: Boolean,
      value: true
    },
    refreshInterval: {
      type: Number,
      value: 30000 // 30秒自动刷新
    }
  },

  data: {
    totalAssets: 0,
    dailyChange: 0,
    dailyChangePercent: 0,
    totalUnrealizedPnL: 0,
    totalUnrealizedPnLPercent: 0,
    loading: false,
    error: null,
    lastUpdated: null,
    formattedAmount: '0.00',
    formattedPercent: '0.00',
    refreshTimer: null
  },

  lifetimes: {
    attached() {
      this.loadAssetData()
      this.startAutoRefresh()
    },
    
    detached() {
      this.stopAutoRefresh()
    }
  },

  methods: {
    // 加载资产数据
    async loadAssetData(forceRefresh = false) {
      if (!this.data.userId) {
        console.warn('用户ID为空，无法加载资产数据')
        return
      }

      this.setData({ 
        loading: true, 
        error: null 
      })

      try {
        const result = await assetCalculationService.getUserTotalAssets(
          this.data.userId, 
          !forceRefresh, // useCache
          forceRefresh   // forceRefresh
        )

        if (result.success) {
          const assetData = result.data
          
          this.setData({
            totalAssets: assetData.totalMarketValue || 0,
            dailyChange: assetData.totalDailyPnL || 0,
            dailyChangePercent: assetData.totalDailyPnLPercent || 0,
            totalUnrealizedPnL: assetData.totalUnrealizedPnL || 0,
            totalUnrealizedPnLPercent: assetData.totalUnrealizedPnLPercent || 0,
            lastUpdated: assetData.lastUpdated || new Date(),
            loading: false,
            error: null
          })

          // 触发数据更新事件
          this.triggerEvent('dataUpdated', {
            assetData: assetData
          })

          console.log('资产数据加载成功:', assetData)
        } else {
          throw new Error(result.error?.message || '加载资产数据失败')
        }
      } catch (error) {
        console.error('加载资产数据失败:', error)
        
        this.setData({
          loading: false,
          error: error.message || '加载失败'
        })

        // 触发错误事件
        this.triggerEvent('error', {
          error: error.message || '加载失败'
        })

        // 显示错误提示
        wx.showToast({
          title: '加载失败',
          icon: 'none',
          duration: 2000
        })
      }
    },

    // 手动刷新数据
    async onRefresh() {
      console.log('手动刷新资产数据')
      await this.loadAssetData(true) // 强制刷新
      
      // 显示刷新成功提示
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    },

    // 重试加载
    async onRetry() {
      console.log('重试加载资产数据')
      await this.loadAssetData(true)
    },

    // 开始自动刷新
    startAutoRefresh() {
      if (!this.data.autoRefresh || this.data.refreshTimer) {
        return
      }

      this.data.refreshTimer = setInterval(() => {
        console.log('自动刷新资产数据')
        this.loadAssetData(false) // 使用缓存
      }, this.data.refreshInterval)

      console.log('自动刷新已启动，间隔:', this.data.refreshInterval, 'ms')
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.data.refreshTimer) {
        clearInterval(this.data.refreshTimer)
        this.data.refreshTimer = null
        console.log('自动刷新已停止')
      }
    },

    // 格式化金额显示
    formatAmount(amount) {
      if (Math.abs(amount) >= 100000000) {
        return (amount / 100000000).toFixed(2) + '亿'
      } else if (Math.abs(amount) >= 10000) {
        return (amount / 10000).toFixed(2) + '万'
      }
      return amount.toFixed(2)
    },

    // 格式化百分比显示
    formatPercent(percent) {
      return Math.abs(percent).toFixed(2)
    },

    // 格式化时间显示
    formatTime(date) {
      if (!date) return ''
      
      const now = new Date()
      const updateTime = new Date(date)
      const diff = now - updateTime
      
      if (diff < 60000) { // 1分钟内
        return '刚刚更新'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 24小时内
        return `${Math.floor(diff / 3600000)}小时前`
      } else {
        return updateTime.toLocaleDateString()
      }
    },

    // 点击资产卡片
    onAssetCardTap() {
      this.triggerEvent('cardTap', {
        totalAssets: this.data.totalAssets,
        dailyChange: this.data.dailyChange,
        dailyChangePercent: this.data.dailyChangePercent
      })
    },

    // 查看详情
    onViewDetail() {
      this.triggerEvent('viewDetail')
    },

    // 格式化时间（在模板中使用）
    formatTime(date) {
      return this.formatTime(date)
    }
  },

  observers: {
    'userId': function(newUserId) {
      if (newUserId) {
        console.log('用户ID变更，重新加载资产数据:', newUserId)
        this.loadAssetData()
      }
    },
    
    'totalAssets': function(newVal) {
      if (newVal !== undefined) {
        this.setData({
          formattedAmount: this.formatAmount(newVal)
        })
      }
    },
    
    'dailyChangePercent': function(newVal) {
      if (newVal !== undefined) {
        this.setData({
          formattedPercent: this.formatPercent(newVal)
        })
      }
    },

    'autoRefresh': function(newVal) {
      if (newVal) {
        this.startAutoRefresh()
      } else {
        this.stopAutoRefresh()
      }
    },

    'lastUpdated': function(newVal) {
      if (newVal) {
        this.setData({
          lastUpdatedText: this.formatTime(newVal)
        })
      }
    }
  }
})