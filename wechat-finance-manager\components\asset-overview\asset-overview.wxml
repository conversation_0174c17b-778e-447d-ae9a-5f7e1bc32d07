<!--components/asset-overview/asset-overview.wxml-->
<view class="asset-overview-container">
  <view class="asset-card {{theme}}" bindtap="onAssetCardTap">
    <!-- 头部 -->
    <view class="asset-header">
      <view class="asset-title">总资产</view>
      <view class="asset-actions">
        <view class="last-updated" wx:if="{{lastUpdated && !loading}}">
          {{lastUpdatedText}}
        </view>
        <view class="refresh-btn" bindtap="onRefresh" wx:if="{{!loading}}">
          <text class="refresh-icon">↻</text>
        </view>
      </view>
    </view>
    
    <!-- 正常内容 -->
    <view class="asset-content" wx:if="{{!loading && !error}}">
      <view class="asset-amount">¥{{formattedAmount}}</view>
      
      <!-- 今日变化 -->
      <view class="asset-change {{dailyChange >= 0 ? 'positive' : 'negative'}}">
        <text class="change-amount">{{dailyChange >= 0 ? '+' : ''}}{{dailyChange.toFixed(2)}}</text>
        <text class="change-percent">({{dailyChangePercent >= 0 ? '+' : ''}}{{formattedPercent}}%)</text>
      </view>
      <view class="asset-subtitle">今日{{dailyChange >= 0 ? '收益' : '亏损'}}</view>
      
      <!-- 累计盈亏 -->
      <view class="asset-total-pnl" wx:if="{{totalUnrealizedPnL !== 0}}">
        <view class="pnl-label">累计盈亏</view>
        <view class="pnl-value {{totalUnrealizedPnL >= 0 ? 'positive' : 'negative'}}">
          <text class="pnl-amount">{{totalUnrealizedPnL >= 0 ? '+' : ''}}{{totalUnrealizedPnL.toFixed(2)}}</text>
          <text class="pnl-percent">({{totalUnrealizedPnLPercent >= 0 ? '+' : ''}}{{totalUnrealizedPnLPercent.toFixed(2)}}%)</text>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view class="asset-loading" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 错误状态 -->
    <view class="asset-error" wx:if="{{error && !loading}}">
      <view class="error-icon">⚠️</view>
      <view class="error-message">{{error}}</view>
      <view class="error-actions">
        <button class="retry-btn" bindtap="onRetry" size="mini">重试</button>
      </view>
    </view>
    
    <!-- 底部操作 -->
    <view class="asset-footer" wx:if="{{!loading && !error}}">
      <view class="view-detail-btn" bindtap="onViewDetail">
        <text class="detail-text">查看详情</text>
        <text class="detail-arrow">→</text>
      </view>
    </view>
  </view>
</view>