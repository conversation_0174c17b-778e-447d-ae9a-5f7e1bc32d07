/* styles/common.wxss - 通用样式 */

/* 全局变量 */
page {
  --primary-color: #007AFF;
  --secondary-color: #5AC8FA;
  --success-color: #34C759;
  --warning-color: #FF9500;
  --error-color: #FF3B30;
  --text-primary: #000000;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --background-primary: #FFFFFF;
  --background-secondary: #F8F8F8;
  --background-tertiary: #F2F2F2;
  --border-color: #E5E5E5;
  --shadow-light: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  --border-radius: 16rpx;
  --border-radius-small: 8rpx;
  --border-radius-large: 24rpx;
  
  /* 新增变量 */
  --bg-color: #F8F8F8;
  --card-bg: #FFFFFF;
  --disabled-color: #D1D1D6;
  --disabled-text-color: #8E8E93;
  --disabled-bg-color: #F2F2F7;
}

/* 深色模式变量 */
page[data-theme="dark"] {
  --text-primary: #FFFFFF;
  --text-secondary: #CCCCCC;
  --text-tertiary: #999999;
  --background-primary: #1C1C1E;
  --background-secondary: #2C2C2E;
  --background-tertiary: #3A3A3C;
  --border-color: #48484A;
  --shadow-light: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 8rpx 30rpx rgba(0, 0, 0, 0.4);
  
  /* 深色模式新增变量 */
  --bg-color: #000000;
  --card-bg: #1C1C1E;
  --card-bg-dark: #2C2C2E;
  --border-color-dark: #48484A;
  --text-primary-dark: #FFFFFF;
  --disabled-color: #48484A;
  --disabled-text-color: #8E8E93;
  --disabled-bg-color: #2C2C2E;
}

/* 基础重置 */
* {
  box-sizing: border-box;
}

page {
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
}

.container-full {
  padding: 0;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background-color: var(--background-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
}

.card-padding {
  padding: 30rpx;
}

.card-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx 30rpx;
  border-top: 1rpx solid var(--border-color);
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 40rpx;
  border-radius: var(--border-radius);
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.btn:active {
  transform: scale(0.98);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-secondary {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-error {
  background-color: var(--error-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-small {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
}

.btn-large {
  padding: 32rpx 48rpx;
  font-size: 36rpx;
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx 20rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--border-radius-small);
  font-size: 32rpx;
  color: var(--text-primary);
  background-color: var(--background-primary);
}

.form-input:focus {
  border-color: var(--primary-color);
}

.form-input-error {
  border-color: var(--error-color);
}

.form-error {
  color: var(--error-color);
  font-size: 24rpx;
  margin-top: 8rpx;
}

.form-help {
  color: var(--text-tertiary);
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 列表样式 */
.list {
  background-color: var(--background-primary);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.list-item {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: var(--background-secondary);
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 8rpx;
}

.list-item-subtitle {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.list-item-extra {
  text-align: right;
}

.list-item-arrow {
  margin-left: 16rpx;
  color: var(--text-tertiary);
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 36rpx;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 间距样式 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }
.m-4 { margin: 40rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }
.mt-4 { margin-top: 40rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }
.mb-4 { margin-bottom: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }
.p-4 { padding: 40rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }
.pt-4 { padding-top: 40rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }
.pb-4 { padding-bottom: 40rpx; }

/* 工具样式 */
.hidden {
  display: none;
}

.visible {
  display: block;
}

.opacity-0 {
  opacity: 0;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-100 {
  opacity: 1;
}

.rounded {
  border-radius: var(--border-radius);
}

.rounded-small {
  border-radius: var(--border-radius-small);
}

.rounded-large {
  border-radius: var(--border-radius-large);
}

.shadow {
  box-shadow: var(--shadow-light);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

/* 动画样式 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 页面过渡动画 */
.page-transition {
  transition: all 0.3s ease;
}

.page-fade-in {
  animation: pageFadeIn 0.3s ease-in-out;
}

.page-fade-out {
  animation: pageFadeOut 0.3s ease-in-out;
}

.page-slide-in-left {
  animation: pageSlideInLeft 0.3s ease-out;
}

.page-slide-out-left {
  animation: pageSlideOutLeft 0.3s ease-in;
}

.page-slide-in-right {
  animation: pageSlideInRight 0.3s ease-out;
}

.page-slide-out-right {
  animation: pageSlideOutRight 0.3s ease-in;
}

.page-scale-in {
  animation: pageScaleIn 0.3s ease-out;
}

.page-scale-out {
  animation: pageScaleOut 0.3s ease-in;
}

@keyframes pageFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pageFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes pageSlideInLeft {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pageSlideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes pageSlideInRight {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pageSlideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes pageScaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pageScaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(1.1);
    opacity: 0;
  }
}