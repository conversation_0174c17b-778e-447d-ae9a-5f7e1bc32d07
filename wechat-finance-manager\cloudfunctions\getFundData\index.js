// cloudfunctions/getFundData/index.js
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 配置
const CONFIG = {
  timeout: 10000,
  maxRetries: 3,
  retryDelay: 1000,
  cacheExpiry: 30 * 60 * 1000 // 30分钟缓存
}

// 基金数据API配置
const FUND_APIS = {
  // 天天基金网API
  eastmoney: {
    info: 'http://fundgz.1234567.com.cn/js/',
    detail: 'https://api.fund.eastmoney.com/f10/lsjz'
  },
  // 新浪基金API
  sina: {
    info: 'https://hq.sinajs.cn/list=fu_'
  }
}

// 日志记录
function log(level, message, data = {}) {
  const logData = {
    level,
    message,
    data: JSON.stringify(data),
    timestamp: new Date(),
    source: 'getFundData'
  }
  
  console.log(`[${level.toUpperCase()}] ${message}`, data)
  
  db.collection('logs').add({ data: logData }).catch(err => {
    console.error('保存日志失败:', err)
  })
}

// 延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 重试执行
async function executeWithRetry(operation, operationName, maxRetries = CONFIG.maxRetries) {
  let lastError
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      log('info', `执行操作 ${operationName}，第 ${attempt} 次尝试`)
      const result = await operation()
      
      if (attempt > 1) {
        log('info', `操作 ${operationName} 在第 ${attempt} 次尝试后成功`)
      }
      
      return result
    } catch (error) {
      lastError = error
      log('warn', `操作 ${operationName} 第 ${attempt} 次尝试失败`, { error: error.message })
      
      if (attempt < maxRetries) {
        await delay(CONFIG.retryDelay * attempt)
      }
    }
  }
  
  throw lastError
}

// 验证基金代码
function validateFundCode(fundCode) {
  if (typeof fundCode !== 'string') return false
  
  // 基金代码：6位数字
  return /^[0-9]{6}$/.test(fundCode)
}

// 从天天基金获取基金数据
async function getFundDataFromEastmoney(fundCode) {
  try {
    const url = `${FUND_APIS.eastmoney.info}${fundCode}.js`
    
    const response = await axios.get(url, {
      timeout: CONFIG.timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'http://fund.eastmoney.com/'
      }
    })
    
    // 解析JSONP响应
    const jsonpMatch = response.data.match(/jsonpgz\((.*)\)/)
    if (!jsonpMatch) {
      throw new Error('无效的响应格式')
    }
    
    const data = JSON.parse(jsonpMatch[1])
    
    if (!data || !data.fundcode) {
      throw new Error('基金数据不存在')
    }
    
    const netValue = parseFloat(data.dwjz) || 0
    const accumulatedValue = parseFloat(data.ljjz) || 0
    const dailyChange = parseFloat(data.jzrq) ? parseFloat(data.gszzl) || 0 : 0
    const estimatedValue = parseFloat(data.gsz) || netValue
    
    return {
      fundCode: data.fundcode,
      name: data.name,
      netValue: netValue,
      accumulatedValue: accumulatedValue,
      estimatedValue: estimatedValue,
      dailyChange: dailyChange,
      dailyChangePercent: dailyChange,
      updateTime: data.gztime || data.jzrq,
      fundType: '混合型', // 默认类型，实际应该从详细信息获取
      fundCompany: '基金公司', // 默认公司，实际应该从详细信息获取
      source: 'eastmoney',
      timestamp: new Date()
    }
    
  } catch (error) {
    log('error', `天天基金数据获取失败: ${fundCode}`, { error: error.message })
    throw error
  }
}

// 从新浪获取基金数据
async function getFundDataFromSina(fundCode) {
  try {
    const url = `${FUND_APIS.sina.info}${fundCode}`
    
    const response = await axios.get(url, {
      timeout: CONFIG.timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    
    const match = response.data.match(/var hq_str_fu_(.+?)="(.+?)";/)
    if (!match) {
      throw new Error('无效的响应格式')
    }
    
    const data = match[2]
    if (!data || data === 'N/A') {
      throw new Error('基金数据不存在')
    }
    
    const parts = data.split(',')
    if (parts.length < 10) {
      throw new Error('数据字段不完整')
    }
    
    const name = parts[0]
    const netValue = parseFloat(parts[1]) || 0
    const accumulatedValue = parseFloat(parts[2]) || 0
    const previousNetValue = parseFloat(parts[3]) || 0
    const dailyChange = netValue - previousNetValue
    const dailyChangePercent = previousNetValue > 0 ? (dailyChange / previousNetValue) * 100 : 0
    
    return {
      fundCode,
      name,
      netValue,
      accumulatedValue,
      estimatedValue: netValue,
      dailyChange: parseFloat(dailyChange.toFixed(4)),
      dailyChangePercent: parseFloat(dailyChangePercent.toFixed(2)),
      updateTime: parts[4] || new Date().toISOString().split('T')[0],
      fundType: '混合型',
      fundCompany: '基金公司',
      source: 'sina',
      timestamp: new Date()
    }
    
  } catch (error) {
    log('error', `新浪基金数据获取失败: ${fundCode}`, { error: error.message })
    throw error
  }
}

// 生成模拟基金数据
function generateMockFundData(fundCode) {
  const baseValue = 1 + Math.random() * 2 // 1-3之间的净值
  const change = (Math.random() - 0.5) * 0.1 // -5%到+5%的变化
  const changePercent = (change / baseValue) * 100
  
  return {
    fundCode,
    name: `模拟基金${fundCode}`,
    netValue: parseFloat(baseValue.toFixed(4)),
    accumulatedValue: parseFloat((baseValue * 1.2).toFixed(4)),
    estimatedValue: parseFloat(baseValue.toFixed(4)),
    dailyChange: parseFloat(change.toFixed(4)),
    dailyChangePercent: parseFloat(changePercent.toFixed(2)),
    updateTime: new Date().toISOString().split('T')[0],
    fundType: '混合型',
    fundCompany: '模拟基金公司',
    source: 'mock',
    timestamp: new Date()
  }
}

// 从缓存获取基金数据
async function getCachedFundData(fundCode) {
  try {
    const cacheExpiry = new Date(Date.now() - CONFIG.cacheExpiry)
    
    const result = await db.collection('fund_data')
      .where({
        fundCode: fundCode,
        createdAt: _.gte(cacheExpiry)
      })
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get()
    
    if (result.data.length > 0) {
      log('info', '从缓存获取基金数据', { fundCode })
      return result.data[0]
    }
    
    return null
  } catch (error) {
    log('error', '获取缓存基金数据失败', { error: error.message })
    return null
  }
}

// 保存基金数据到数据库
async function saveFundDataToDatabase(fundData) {
  try {
    await db.collection('fund_data').add({
      data: {
        ...fundData,
        createdAt: new Date()
      }
    })
    
    log('info', '基金数据保存成功', { fundCode: fundData.fundCode })
  } catch (error) {
    log('error', '保存基金数据失败', { error: error.message })
    throw error
  }
}

// 获取基金数据的主函数
async function getRealTimeFundData(fundCode) {
  let fundData = null
  let errors = []
  
  // 尝试从天天基金获取
  try {
    log('info', '尝试从天天基金获取数据', { fundCode })
    fundData = await executeWithRetry(
      () => getFundDataFromEastmoney(fundCode),
      'getFundDataFromEastmoney'
    )
    
    if (fundData) {
      log('info', '天天基金数据获取成功', { fundCode })
      return fundData
    }
  } catch (error) {
    errors.push({ source: 'eastmoney', error: error.message })
    log('warn', '天天基金数据获取失败', { error: error.message })
  }
  
  // 如果天天基金失败，尝试新浪
  try {
    log('info', '尝试从新浪获取基金数据', { fundCode })
    fundData = await executeWithRetry(
      () => getFundDataFromSina(fundCode),
      'getFundDataFromSina'
    )
    
    if (fundData) {
      log('info', '新浪基金数据获取成功', { fundCode })
      return fundData
    }
  } catch (error) {
    errors.push({ source: 'sina', error: error.message })
    log('warn', '新浪基金数据获取失败', { error: error.message })
  }
  
  // 如果都失败，生成模拟数据
  log('warn', '所有数据源失败，使用模拟数据', { fundCode, errors })
  return generateMockFundData(fundCode)
}

// 云函数入口
exports.main = async (event, context) => {
  const { fundCode, forceRefresh = false } = event
  
  try {
    log('info', '开始获取基金数据', { fundCode, forceRefresh })
    
    // 参数验证
    if (!fundCode) {
      throw new Error('基金代码不能为空')
    }
    
    if (!validateFundCode(fundCode)) {
      throw new Error('基金代码格式不正确')
    }
    
    let fundData = null
    
    // 如果不强制刷新，先尝试从缓存获取
    if (!forceRefresh) {
      fundData = await getCachedFundData(fundCode)
      
      if (fundData) {
        log('info', '使用缓存的基金数据', { fundCode })
        return {
          success: true,
          data: fundData,
          meta: {
            source: 'cache',
            timestamp: new Date()
          }
        }
      }
    }
    
    // 获取实时基金数据
    fundData = await getRealTimeFundData(fundCode)
    
    // 保存到数据库
    await saveFundDataToDatabase(fundData)
    
    log('info', '基金数据获取完成', { fundCode, source: fundData.source })
    
    return {
      success: true,
      data: fundData,
      meta: {
        source: fundData.source,
        timestamp: new Date()
      }
    }
    
  } catch (error) {
    log('error', '获取基金数据失败', { error: error.message, stack: error.stack })
    
    return {
      success: false,
      error: error.message,
      meta: {
        timestamp: new Date()
      }
    }
  }
}