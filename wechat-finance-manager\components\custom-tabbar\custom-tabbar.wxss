/* components/custom-tabbar/custom-tabbar.wxss */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--background-primary);
  border-top: 1rpx solid var(--border-color);
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 1000;
}

.tabbar-container {
  display: flex;
  height: 100rpx;
  position: relative;
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  transition: all 0.3s ease;
  position: relative;
}

.tabbar-item.active {
  transform: translateY(-4rpx);
}

.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4rpx;
  transition: all 0.3s ease;
}

.tabbar-item.active .tabbar-icon {
  transform: scale(1.1);
}

.icon-text {
  font-size: 40rpx;
  transition: all 0.3s ease;
}

.tabbar-item.active .icon-text {
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 122, 255, 0.3));
}

.tabbar-text {
  font-size: 20rpx;
  color: var(--text-tertiary);
  transition: all 0.3s ease;
  font-weight: 400;
}

.tabbar-item.active .tabbar-text {
  color: var(--primary-color);
  font-weight: 500;
  transform: scale(1.05);
}

.active-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 20%;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2rpx 2rpx 0 0;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色模式适配 */
.custom-tabbar.dark {
  background: var(--background-primary);
  border-top-color: var(--border-color);
}

/* 点击效果 */
.tabbar-item:active {
  background: var(--background-secondary);
  border-radius: 16rpx;
}

/* 动画效果 */
@keyframes tabSwitch {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.tabbar-item.switching {
  animation: tabSwitch 0.2s ease;
}