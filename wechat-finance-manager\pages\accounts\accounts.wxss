/* pages/accounts/accounts.wxss */
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.add-button {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 2rpx dashed #007AFF;
  transition: transform 0.2s ease;
}

.add-button:active {
  transform: scale(0.98);
}

.add-icon {
  font-size: 40rpx;
  color: #007AFF;
  margin-right: 16rpx;
}

.add-text {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: 500;
}

.refresh-hint {
  text-align: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.refresh-text {
  font-size: 28rpx;
  color: #007AFF;
}

.account-list {
  display: flex;
  flex-direction: column;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #000000;
  }
  
  .add-button {
    background: #1c1c1e;
    border-color: #007AFF;
  }
  
  .empty-text {
    color: #ffffff;
  }
  
  .empty-desc {
    color: #8e8e93;
  }
  
  .loading-text {
    color: #8e8e93;
  }
  
  .loading-spinner {
    border-color: #3a3a3c;
    border-top-color: #007AFF;
  }
}